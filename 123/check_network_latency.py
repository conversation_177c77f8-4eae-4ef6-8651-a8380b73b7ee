#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 网络延迟检测工具 - 分析到各交易所的真实延迟
帮助理解为什么会出现订单簿数据非同步问题
"""

import asyncio
import aiohttp
import time
import subprocess
import sys
from typing import Dict, List, Tuple

class NetworkLatencyChecker:
    """网络延迟检测器"""
    
    def __init__(self):
        self.exchange_endpoints = {
            "gate": {
                "api": "https://api.gateio.ws/api/v4/spot/time",
                "ws": "wss://api.gateio.ws/ws/v4/",
                "servers": ["api.gateio.ws", "www.gate.io"]
            },
            "bybit": {
                "api": "https://api.bybit.com/v5/market/time",
                "ws": "wss://stream.bybit.com/v5/public/spot",
                "servers": ["api.bybit.com", "www.bybit.com"]
            },
            "okx": {
                "api": "https://www.okx.com/api/v5/public/time",
                "ws": "wss://ws.okx.com:8443/ws/v5/public",
                "servers": ["www.okx.com", "aws.okx.com"]
            }
        }
    
    async def check_all_latency(self):
        """检查所有交易所的延迟"""
        print("🔍 开始检测网络延迟...")
        print("=" * 80)
        
        # 1. 检测基础网络连接
        await self.check_basic_connectivity()
        
        # 2. 检测到各交易所的Ping延迟
        await self.check_ping_latency()
        
        # 3. 检测API响应延迟
        await self.check_api_latency()
        
        # 4. 检测时间同步差异
        await self.check_time_sync()
        
        # 5. 分析结果
        self.analyze_results()
    
    async def check_basic_connectivity(self):
        """检查基础网络连接"""
        print("\n📡 1. 基础网络连接检测")
        print("-" * 50)
        
        # 检测到知名服务器的延迟作为基准
        base_servers = [
            ("Google DNS", "8.8.8.8"),
            ("Cloudflare DNS", "1.1.1.1"),
            ("阿里云DNS", "223.5.5.5"),
            ("腾讯云DNS", "119.29.29.29")
        ]
        
        for name, server in base_servers:
            latency = await self.ping_server(server)
            if latency:
                print(f"  ✅ {name:15} ({server:15}): {latency:6.1f}ms")
            else:
                print(f"  ❌ {name:15} ({server:15}): 超时")
    
    async def check_ping_latency(self):
        """检查到各交易所的Ping延迟"""
        print("\n🏪 2. 交易所服务器Ping延迟")
        print("-" * 50)
        
        for exchange, config in self.exchange_endpoints.items():
            print(f"\n  📊 {exchange.upper()}交易所:")
            
            for server in config["servers"]:
                latency = await self.ping_server(server)
                if latency:
                    status = self.get_latency_status(latency)
                    print(f"    {status} {server:20}: {latency:6.1f}ms")
                else:
                    print(f"    ❌ {server:20}: 超时/无法连接")
    
    async def check_api_latency(self):
        """检查API响应延迟"""
        print("\n⚡ 3. API响应延迟测试")
        print("-" * 50)
        
        for exchange, config in self.exchange_endpoints.items():
            print(f"\n  📊 {exchange.upper()} API测试:")
            
            # 测试多次获取平均值
            latencies = []
            success_count = 0
            
            for i in range(5):
                try:
                    # 🔥 使用统一HTTP会话管理器和重试机制
                    from core.unified_http_session_manager import get_unified_session_manager
                    session_manager = get_unified_session_manager()

                    start_time = time.time()

                    # 使用重试机制进行API调用
                    data = await session_manager.fetch_with_retry(
                        exchange_name=exchange,
                        url=config["api"],
                        method="GET"
                    )

                    if data:
                        end_time = time.time()
                        latency = (end_time - start_time) * 1000
                        latencies.append(latency)
                        success_count += 1

                        print(f"    第{i+1}次: {latency:6.1f}ms {self.get_latency_status(latency)} 🔥")
                    else:
                        print(f"    第{i+1}次: 重试机制失败 ❌")

                except Exception as e:
                    print(f"    第{i+1}次: 异常 - {str(e)[:50]} ❌")
                
                # 间隔100ms
                await asyncio.sleep(0.1)
            
            # 计算统计数据
            if latencies:
                avg_latency = sum(latencies) / len(latencies)
                min_latency = min(latencies)
                max_latency = max(latencies)
                
                print(f"    📈 统计: 平均{avg_latency:6.1f}ms | 最小{min_latency:6.1f}ms | 最大{max_latency:6.1f}ms | 成功率{success_count}/5")
            else:
                print(f"    ❌ 所有API请求都失败了")
    
    async def check_time_sync(self):
        """检查时间同步差异"""
        print("\n⏰ 4. 服务器时间同步检测")
        print("-" * 50)
        
        local_time = time.time() * 1000  # 本地时间（毫秒）
        server_times = {}
        
        # 🔥 使用统一HTTP会话管理器和重试机制
        from core.unified_http_session_manager import get_unified_session_manager
        session_manager = get_unified_session_manager()

        for exchange, config in self.exchange_endpoints.items():
            try:
                # 使用重试机制进行API调用
                data = await session_manager.fetch_with_retry(
                    exchange_name=exchange,
                    url=config["api"],
                    method="GET"
                )

                if data:
                    server_time = self.extract_server_time(exchange, data)

                    if server_time:
                        server_times[exchange] = server_time
                        time_diff = abs(server_time - local_time)

                        print(f"  📊 {exchange.upper():5}: 服务器时间差 {time_diff:7.1f}ms {self.get_sync_status(time_diff)} 🔥")
                    else:
                        print(f"  ❌ {exchange.upper():5}: 无法解析服务器时间")
                else:
                    print(f"  ❌ {exchange.upper():5}: 重试机制失败")

            except Exception as e:
                print(f"  ❌ {exchange.upper():5}: 异常 - {str(e)[:40]}")
        
        # 分析交易所之间的时间差
        if len(server_times) >= 2:
            print(f"\n  🔄 交易所间时间差分析:")
            exchanges = list(server_times.keys())
            for i in range(len(exchanges)):
                for j in range(i+1, len(exchanges)):
                    ex1, ex2 = exchanges[i], exchanges[j]
                    time_diff = abs(server_times[ex1] - server_times[ex2])
                    print(f"    {ex1.upper()} ↔ {ex2.upper()}: {time_diff:7.1f}ms {self.get_sync_status(time_diff)}")
    
    def extract_server_time(self, exchange: str, data: dict) -> int:
        """🔥 修复：提取服务器时间 - 使用统一的时间戳处理逻辑"""
        try:
            if exchange == "gate":
                return int(data.get("server_time", 0))
            elif exchange == "bybit":
                result = data.get("result", {})
                # 🔥 优先使用timeNano（纳秒级，最精确）
                if "timeNano" in result:
                    try:
                        return int(result["timeNano"]) // 1000000
                    except (ValueError, TypeError):
                        pass
                # 🔥 备用方案：使用timeSecond（秒级）
                if "timeSecond" in result:
                    try:
                        return int(result["timeSecond"]) * 1000
                    except (ValueError, TypeError):
                        pass
                # 🔥 第三备用方案：使用顶级time字段
                if "time" in data:
                    try:
                        return int(data["time"])
                    except (ValueError, TypeError):
                        pass
            elif exchange == "okx":
                return int(data.get("data", [{}])[0].get("ts", 0))
        except:
            pass
        return 0
    
    async def ping_server(self, host: str) -> float:
        """Ping服务器获取延迟"""
        try:
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', '3000', host]
            else:
                cmd = ['ping', '-c', '1', '-W', '3', host]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode()
                
                # 解析ping结果
                if sys.platform.startswith('win'):
                    # Windows: "时间=123ms" 或 "time=123ms"
                    import re
                    match = re.search(r'时间[<=](\d+)ms|time[<=](\d+)ms', output, re.IGNORECASE)
                    if match:
                        return float(match.group(1) or match.group(2))
                else:
                    # Linux: "time=123 ms" 或 "time=123ms"
                    import re
                    match = re.search(r'time=(\d+\.?\d*)\s*ms', output, re.IGNORECASE)
                    if match:
                        return float(match.group(1))
            
            return None
            
        except Exception:
            return None
    
    def get_latency_status(self, latency: float) -> str:
        """获取延迟状态图标"""
        if latency < 50:
            return "🟢"  # 优秀
        elif latency < 100:
            return "🟡"  # 良好
        elif latency < 200:
            return "🟠"  # 一般
        else:
            return "🔴"  # 较差
    
    def get_sync_status(self, time_diff: float) -> str:
        """获取同步状态"""
        if time_diff < 100:
            return "🟢 同步良好"
        elif time_diff < 500:
            return "🟡 同步一般"
        elif time_diff < 1000:
            return "🟠 同步较差"
        else:
            return "🔴 严重不同步"
    
    def analyze_results(self):
        """分析结果并给出建议"""
        print("\n" + "=" * 80)
        print("📋 分析结果和建议")
        print("=" * 80)
        
        print("""
🎯 关键理解：
  1. 200M带宽 ≠ 低延迟
     • 带宽是"管道大小"，延迟是"传输距离"
     • 你的200M带宽完全够用，不是瓶颈
  
  2. 网络延迟的来源：
     • 🌍 地理距离：VPS到交易所服务器的物理距离
     • 🛤️  网络路由：数据包经过的网络节点数量
     • 🏢 服务器响应：交易所服务器的处理时间
     • 🌐 网络拥堵：网络高峰期的影响

🔧 现实情况分析：
  • 如果延迟 < 100ms  → 🟢 优秀，很少见
  • 如果延迟 100-300ms → 🟡 正常，大部分VPS的情况  
  • 如果延迟 300-600ms → 🟠 一般，可以使用
  • 如果延迟 > 600ms  → 🔴 较差，需要优化

💡 解决建议：
  1. 调整同步阈值：max_time_diff_ms = 3000ms
  2. 选择更近的VPS：香港/新加坡机房
  3. 使用CDN加速：如果交易所支持
  4. 优化代码：使用相对时间而非绝对同步

⚠️  重要提醒：
  你看到的2713ms时间差是正常的！
  这不是你的网络问题，而是系统设计过于严格。
        """)

async def main():
    """主函数"""
    checker = NetworkLatencyChecker()
    await checker.check_all_latency()

if __name__ == "__main__":
    print("🔍 网络延迟分析工具")
    print("分析为什么会出现'订单簿数据非同步'警告")
    print("这不是你的200M网速问题！")
    
    asyncio.run(main()) 