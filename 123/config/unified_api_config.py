"""
🔥 统一API调用配置
集中管理所有API相关的配置参数，避免配置分散问题
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ExchangeRateConfig:
    """交易所限速配置"""
    rate_limit: int  # 每秒请求数
    cooldown: float  # 基础冷却时间
    burst_limit: int  # 突发请求限制
    retry_delay: float  # 重试延迟


class UnifiedAPIConfig:
    """🔥 统一API配置类 - 集中管理所有API相关配置"""
    
    # 🔥 **核心配置**：三交易所统一限速配置
    EXCHANGE_RATE_CONFIGS = {
        "gate": ExchangeRateConfig(
            rate_limit=8,      # Gate.io: 8次/秒，确保健壮性
            cooldown=1.5,      # 基础冷却1.5秒
            burst_limit=12,    # 突发限制12次
            retry_delay=2.0    # 重试延迟2秒
        ),
        "bybit": ExchangeRateConfig(
            rate_limit=4,      # Bybit: 4次/秒，确保健壮性
            cooldown=1.5,      # 基础冷却1.5秒
            burst_limit=6,     # 突发限制6次
            retry_delay=2.0    # 重试延迟2秒
        ),
        "okx": ExchangeRateConfig(
            rate_limit=1,      # 🔥 修复：OKX降低到1次/秒，避免50011限流错误
            cooldown=1.0,      # 基础冷却1.0秒
            burst_limit=2,     # 突发限制2次
            retry_delay=5.0    # 重试延迟5秒（OKX限速更严格）
        )
    }
    
    # 🔥 **冷却配置**：统一冷却时间策略
    COOLDOWN_CONFIG = {
        "base_cooldown": float(os.getenv("API_BASE_COOLDOWN", "1.5")),
        "buffer_cooldown": float(os.getenv("API_BUFFER_COOLDOWN", "3.5")),
        "total_cooldown": float(os.getenv("API_TOTAL_COOLDOWN", "5.0")),
        "error_cooldown": float(os.getenv("API_ERROR_COOLDOWN", "10.0"))
    }
    
    # 🔥 **批处理配置**：启动优化相关
    BATCH_CONFIG = {
        "batch_size": int(os.getenv("API_BATCH_SIZE", "5")),
        "batch_cooldown": float(os.getenv("API_BATCH_COOLDOWN", "2.0")),
        "max_parallel_requests": int(os.getenv("API_MAX_PARALLEL", "10")),
        "startup_delay": float(os.getenv("API_STARTUP_DELAY", "1.0"))
    }
    
    # 🔥 **重试配置**：统一重试策略
    RETRY_CONFIG = {
        "max_retries": int(os.getenv("API_MAX_RETRIES", "3")),
        "exponential_base": float(os.getenv("API_RETRY_BASE", "2.0")),
        "max_retry_delay": float(os.getenv("API_MAX_RETRY_DELAY", "60.0")),
        "jitter_factor": float(os.getenv("API_RETRY_JITTER", "0.1"))
    }
    
    # 🔥 **超时配置**：统一超时设置
    TIMEOUT_CONFIG = {
        "connection_timeout": float(os.getenv("API_CONNECT_TIMEOUT", "10.0")),
        "read_timeout": float(os.getenv("API_READ_TIMEOUT", "30.0")),
        "total_timeout": float(os.getenv("API_TOTAL_TIMEOUT", "45.0")),
        "keep_alive_timeout": float(os.getenv("API_KEEPALIVE_TIMEOUT", "300.0"))
    }
    
    # 🔥 **错误处理配置**
    ERROR_HANDLING_CONFIG = {
        "rate_limit_errors": ["429", "Too Many Requests", "50011", "10006"],
        "network_errors": ["timeout", "connection", "dns"],
        "retry_on_errors": ["500", "502", "503", "504"],
        "permanent_errors": ["401", "403", "400"]
    }
    
    @classmethod
    def get_exchange_config(cls, exchange: str) -> Optional[ExchangeRateConfig]:
        """获取指定交易所的配置"""
        return cls.EXCHANGE_RATE_CONFIGS.get(exchange.lower())
    
    @classmethod
    def get_rate_limit(cls, exchange: str) -> int:
        """获取交易所限速配置"""
        config = cls.get_exchange_config(exchange)
        return config.rate_limit if config else 10
    
    @classmethod
    def get_cooldown(cls, exchange: str) -> float:
        """获取交易所冷却时间"""
        config = cls.get_exchange_config(exchange)
        return config.cooldown if config else 1.0
    
    @classmethod
    def get_retry_delay(cls, exchange: str) -> float:
        """获取交易所重试延迟"""
        config = cls.get_exchange_config(exchange)
        return config.retry_delay if config else 2.0
    
    @classmethod
    def is_rate_limit_error(cls, error_msg: str) -> bool:
        """判断是否为限速错误"""
        error_msg_lower = str(error_msg).lower()
        return any(err in error_msg_lower for err in cls.ERROR_HANDLING_CONFIG["rate_limit_errors"])
    
    @classmethod
    def should_retry_error(cls, error_msg: str) -> bool:
        """判断错误是否应该重试"""
        error_msg_str = str(error_msg)
        return any(err in error_msg_str for err in cls.ERROR_HANDLING_CONFIG["retry_on_errors"])
    
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """验证配置完整性"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证交易所配置
        for exchange, config in cls.EXCHANGE_RATE_CONFIGS.items():
            if config.rate_limit <= 0:
                validation_result["errors"].append(f"{exchange}: rate_limit必须大于0")
                validation_result["valid"] = False
            
            if config.cooldown < 0:
                validation_result["errors"].append(f"{exchange}: cooldown不能为负数")
                validation_result["valid"] = False
        
        # 验证批处理配置
        if cls.BATCH_CONFIG["batch_size"] <= 0:
            validation_result["errors"].append("batch_size必须大于0")
            validation_result["valid"] = False
        
        return validation_result


# 🔥 **全局配置实例**
_unified_api_config = UnifiedAPIConfig()

def get_unified_api_config() -> UnifiedAPIConfig:
    """获取统一API配置实例"""
    return _unified_api_config

def get_exchange_rate_limit(exchange: str) -> int:
    """便捷函数：获取交易所限速"""
    return _unified_api_config.get_rate_limit(exchange)

def get_exchange_cooldown(exchange: str) -> float:
    """便捷函数：获取交易所冷却时间"""
    return _unified_api_config.get_cooldown(exchange)