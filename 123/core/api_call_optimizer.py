#!/usr/bin/env python3
"""
API调用优化器 - 解决启动阶段API限速问题
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional
from collections import defaultdict

logger = logging.getLogger("api_optimizer")

class APICallOptimizer:
    """API调用优化器"""
    
    def __init__(self):
        self.logger = logger
        
        # 🔥 **配置统一化**：使用统一API配置，消除配置分散问题
        try:
            from config.unified_api_config import get_unified_api_config
            self.unified_config = get_unified_api_config()
            
            # 🔥 从统一配置加载限速配置
            self.rate_limits = {
                exchange: self.unified_config.get_rate_limit(exchange)
                for exchange in ["gate", "bybit", "okx"]
            }
            
            # 🔥 从统一配置加载冷却配置
            self.cooldown_config = self.unified_config.COOLDOWN_CONFIG.copy()
            
            # 🔥 从统一配置加载批处理配置
            self.batch_config = self.unified_config.BATCH_CONFIG.copy()
            
            # 🔥 从统一配置加载重试配置
            self.retry_config = self.unified_config.RETRY_CONFIG.copy()
            
            self.logger.info("✅ 使用统一API配置文件")
            self.logger.info(f"🔧 限速配置: {self.rate_limits}")
            
        except ImportError:
            # 🔥 **兜底配置**：统一API配置不可用时的默认值
            self.logger.warning("⚠️ 统一API配置不可用，使用默认配置")
            
            self.rate_limits = {
                "gate": 8,     # 降低限制，确保健壮性
                "bybit": 4,    # 降低限制，确保健壮性
                "okx": 2       # 平衡限速和WebSocket性能
            }
            
            self.cooldown_config = {
                "base_cooldown": 1.5,
                "buffer_cooldown": 3.5,
                "total_cooldown": 5.0
            }
            
            self.batch_config = {
                "batch_size": 5,
                "batch_cooldown": 2.0,
                "max_parallel_requests": 10
            }
            
            self.retry_config = {
                "max_retries": 3,
                "exponential_base": 2.0,
                "max_retry_delay": 60.0
            }
        
        # 调用队列
        self.call_queues = {
            "gate": asyncio.Queue(),
            "bybit": asyncio.Queue(),
            "okx": asyncio.Queue()
        }
        
        # 调用统计
        self.call_stats = defaultdict(int)
        
    async def optimize_startup_api_calls(self, exchanges: Dict[str, Any], symbols: List[str]):
        """🔥 **配置统一化**：分批预加载，使用统一配置参数确保30+代币健壮启动"""
        self.logger.info("🚀 开始分批预加载API调用优化...")

        # 🔥 使用统一配置参数
        batch_size = getattr(self, 'batch_config', {}).get('batch_size', 5)
        batch_cooldown = getattr(self, 'batch_config', {}).get('batch_cooldown', 2.0)
        startup_delay = getattr(self, 'batch_config', {}).get('startup_delay', 1.0)
        
        self.logger.info(f"🔧 使用统一配置: 批大小={batch_size}, 批冷却={batch_cooldown}秒, 启动延迟={startup_delay}秒")
        
        # 启动延迟
        if startup_delay > 0:
            self.logger.info(f"⏰ 启动延迟: {startup_delay}秒...")
            await asyncio.sleep(startup_delay)

        # 1. 分批并行化交易规则预加载
        await self.batched_trading_rules_preload(exchanges, symbols, batch_size, batch_cooldown)

        # 2. 批量化余额查询（低频率）
        await self.batch_balance_queries(exchanges)

        # 3. 分批智能化合约信息获取
        await self.batched_contract_info_fetch(exchanges, symbols, batch_size, batch_cooldown)

        # 4. 延迟杠杆设置（最后执行）
        await self.delayed_leverage_setup(exchanges, symbols)

        self.logger.info("✅ 分批预加载API调用优化完成")
        
    async def parallel_trading_rules_preload(self, exchanges: Dict[str, Any], symbols: List[str]):
        """并行化交易规则预加载"""
        self.logger.info("📋 并行化交易规则预加载...")
        
        tasks = []
        
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                for market_type in ["spot", "futures"]:
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._get_trading_rule,
                        exchange, symbol, market_type
                    )
                    tasks.append(task)
        
        # 并行执行，但受限于各交易所的API限制
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 交易规则预加载完成: {success_count}/{len(tasks)}")

    async def test_rate_limiting_effectiveness(self, exchange_name: str = "gate", test_calls: int = 5) -> Dict[str, Any]:
        """🔥 **配置统一化**：测试限速效果，使用统一配置参数"""
        self.logger.info(f"🧪 测试{exchange_name}限速效果，调用次数: {test_calls}")

        rate_limit = self.rate_limits[exchange_name]
        expected_min_duration = (test_calls - 1) / rate_limit  # 前n-1次调用的最小间隔时间
        
        # 🔥 使用统一配置的超时设置
        api_timeout = getattr(self, 'timeout_config', {}).get('read_timeout', 30.0)

        # 模拟API调用函数
        async def mock_api_call():
            await asyncio.sleep(0.01)  # 模拟10ms的API响应时间
            return {"status": "success", "timestamp": time.time()}

        # 执行测试
        test_start = time.time()
        results = []

        for i in range(test_calls):
            call_start = time.time()
            try:
                result = await asyncio.wait_for(
                    self.rate_limited_api_call(exchange_name, mock_api_call),
                    timeout=api_timeout
                )
            except asyncio.TimeoutError:
                result = None
                self.logger.warning(f"😨 {exchange_name}测试调用{i+1}超时")
            
            call_end = time.time()

            results.append({
                "call_index": i,
                "call_duration": call_end - call_start,
                "timestamp": call_end,
                "result": result is not None
            })

        test_duration = time.time() - test_start
        
        # 🔥 使用统一配置的容错率
        error_tolerance = getattr(self, 'retry_config', {}).get('jitter_factor', 0.1)

        # 分析结果
        analysis = {
            "exchange": exchange_name,
            "rate_limit_per_sec": rate_limit,
            "test_calls": test_calls,
            "total_duration": test_duration,
            "expected_min_duration": expected_min_duration,
            "rate_limiting_working": test_duration >= expected_min_duration * (1 - error_tolerance),
            "average_call_interval": test_duration / max(test_calls - 1, 1),
            "expected_interval": 1.0 / rate_limit,
            "timeout_setting": api_timeout,
            "error_tolerance": error_tolerance,
            "results": results
        }

        self.logger.info(f"📊 {exchange_name}限速测试结果:")
        self.logger.info(f"   总耗时: {test_duration:.3f}秒 (预期最小: {expected_min_duration:.3f}秒)")
        self.logger.info(f"   平均间隔: {analysis['average_call_interval']:.3f}秒 (预期: {analysis['expected_interval']:.3f}秒)")
        self.logger.info(f"   限速有效: {'✅' if analysis['rate_limiting_working'] else '❌'}")
        self.logger.info(f"   配置参数: 超时={api_timeout}秒, 容错率={error_tolerance}")

        return analysis

    async def _rate_limit_wait(self, exchange_name: str):
        """🔥 **配置统一化**：健壮的限速等待方法，使用统一配置参数"""
        rate_limit = self.rate_limits.get(exchange_name, 10)

        # 🔥 使用统一配置的限速控制
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_name}_last_call", 0)
        time_since_last = current_time - last_call_time
        
        # 🔥 使用统一配置的冷却时间
        base_cooldown = getattr(self, 'cooldown_config', {}).get('base_cooldown', 1.5)
        buffer_cooldown = getattr(self, 'cooldown_config', {}).get('buffer_cooldown', 3.5)

        # 🔥 优化修复：合理的冷却时间，平衡限速和性能
        if exchange_name == "okx":
            # OKX使用优化的冷却策略 - 调整到支持统一配置
            min_interval = 1.0 / rate_limit  # 直接使用配置的限速
        else:
            # 其他交易所使用基础冷却 + 缓冲
            base_interval = 1.0 / rate_limit
            min_interval = max(base_interval, base_cooldown)

        # 🔥 确保严格遵守冷却时间
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.info(f"🕐 {exchange_name} 健壮冷却等待: {wait_time:.3f}秒 (要求: {min_interval:.3f}秒)")
            await asyncio.sleep(wait_time)

        # 🔥 修复：在等待完成后立即更新时间戳
        setattr(self, f"_{exchange_name}_last_call", time.time())

    async def batched_trading_rules_preload(self, exchanges: Dict[str, Any], symbols: List[str],
                                          batch_size: int = None, batch_cooldown: float = None):
        """🔥 **配置统一化**：分批预加载交易规则，使用统一配置参数"""
        
        # 🔥 使用统一配置或传入参数
        if batch_size is None:
            batch_size = getattr(self, 'batch_config', {}).get('batch_size', 5)
        if batch_cooldown is None:
            batch_cooldown = getattr(self, 'batch_config', {}).get('batch_cooldown', 2.0)
            
        self.logger.info(f"📦 开始分批预加载交易规则: 每批{batch_size}个，批间冷却{batch_cooldown}秒")

        # 创建所有任务
        all_tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                for market_type in ["spot", "futures"]:
                    task_info = {
                        "exchange_name": exchange_name,
                        "exchange": exchange,
                        "symbol": symbol,
                        "market_type": market_type
                    }
                    all_tasks.append(task_info)

        # 分批处理
        total_batches = (len(all_tasks) + batch_size - 1) // batch_size
        self.logger.info(f"📊 总任务数: {len(all_tasks)}, 分为{total_batches}批")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(all_tasks))
            batch_tasks = all_tasks[start_idx:end_idx]

            self.logger.info(f"🔄 处理第{batch_idx + 1}/{total_batches}批 ({len(batch_tasks)}个任务)")

            # 并行执行当前批次
            batch_coroutines = []
            for task_info in batch_tasks:
                coro = self.rate_limited_api_call(
                    task_info["exchange_name"],
                    self._get_trading_rule,
                    task_info["exchange"], task_info["symbol"], task_info["market_type"]
                )
                batch_coroutines.append(coro)

            # 执行当前批次
            batch_start = time.time()
            results = await asyncio.gather(*batch_coroutines, return_exceptions=True)
            batch_duration = time.time() - batch_start

            # 统计结果
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.info(f"✅ 第{batch_idx + 1}批完成: {success_count}/{len(batch_tasks)}成功, 耗时{batch_duration:.1f}秒")

            # 批间冷却（除了最后一批）
            if batch_idx < total_batches - 1:
                self.logger.info(f"🕐 批间冷却: {batch_cooldown}秒...")
                await asyncio.sleep(batch_cooldown)

        self.logger.info("✅ 分批预加载交易规则完成")

    async def batched_contract_info_fetch(self, exchanges: Dict[str, Any], symbols: List[str],
                                        batch_size: int = None, batch_cooldown: float = None):
        """🔥 **配置统一化**：分批获取合约信息，使用统一配置参数"""
        
        # 🔥 使用统一配置或传入参数
        if batch_size is None:
            batch_size = getattr(self, 'batch_config', {}).get('batch_size', 5)
        if batch_cooldown is None:
            batch_cooldown = getattr(self, 'batch_config', {}).get('batch_cooldown', 2.0)
            
        self.logger.info(f"📦 开始分批获取合约信息: 每批{batch_size}个，批间冷却{batch_cooldown}秒")

        # 创建合约信息任务
        contract_tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'get_contract_info'):
                    contract_tasks.append({
                        "exchange_name": exchange_name,
                        "exchange": exchange,
                        "symbol": symbol
                    })

        if not contract_tasks:
            self.logger.info("📋 无合约信息需要获取")
            return

        # 分批处理
        total_batches = (len(contract_tasks) + batch_size - 1) // batch_size
        self.logger.info(f"📊 合约信息任务数: {len(contract_tasks)}, 分为{total_batches}批")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(contract_tasks))
            batch_tasks = contract_tasks[start_idx:end_idx]

            self.logger.info(f"🔄 处理合约信息第{batch_idx + 1}/{total_batches}批")

            # 并行执行当前批次
            batch_coroutines = []
            for task_info in batch_tasks:
                coro = self.rate_limited_api_call(
                    task_info["exchange_name"],
                    self._get_contract_info,
                    task_info["exchange"], task_info["symbol"]
                )
                batch_coroutines.append(coro)

            # 执行当前批次
            results = await asyncio.gather(*batch_coroutines, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.info(f"✅ 合约信息第{batch_idx + 1}批完成: {success_count}/{len(batch_tasks)}成功")

            # 批间冷却
            if batch_idx < total_batches - 1:
                await asyncio.sleep(batch_cooldown)

        self.logger.info("✅ 分批获取合约信息完成")

    async def rate_limited_api_call(self, exchange_name: str, func, *args, **kwargs):
        """限速API调用"""
        # 添加到调用队列
        await self.call_queues[exchange_name].put((func, args, kwargs))
        
        # 等待执行
        return await self._execute_rate_limited_call(exchange_name)
        
    async def _execute_rate_limited_call(self, exchange_name: str):
        """🔥 **配置统一化**：执行限速调用，使用统一配置参数"""
        queue = self.call_queues[exchange_name]
        rate_limit = self.rate_limits[exchange_name]

        if queue.empty():
            return None

        func, args, kwargs = await queue.get()

        # 🔥 使用统一配置的限速控制逻辑
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_name}_last_call", 0)
        time_since_last = current_time - last_call_time
        min_interval = 1.0 / rate_limit
        
        # 🔥 使用统一配置的冷却时间
        base_cooldown = getattr(self, 'cooldown_config', {}).get('base_cooldown', 1.5)
        min_interval = max(min_interval, base_cooldown)

        # 🔥 关键修复：确保严格遵守最小间隔
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.debug(f"🕐 {exchange_name} API限速等待: {wait_time:.3f}秒 (间隔要求: {min_interval:.3f}秒)")
            await asyncio.sleep(wait_time)

        # 🔥 修复：在执行调用前立即更新时间戳，确保精确间隔控制
        call_start_time = time.time()
        setattr(self, f"_{exchange_name}_last_call", call_start_time)
        
        # 🔥 使用统一配置的超时设置
        api_timeout = getattr(self, 'timeout_config', {}).get('total_timeout', 45.0)

        # 执行调用
        try:
            result = await asyncio.wait_for(func(*args, **kwargs), timeout=api_timeout)
            call_duration = time.time() - call_start_time
            self.call_stats[exchange_name] += 1

            self.logger.debug(f"✅ {exchange_name} API调用成功，耗时: {call_duration:.3f}秒")
            return result

        except asyncio.TimeoutError:
            call_duration = time.time() - call_start_time
            self.logger.error(f"⏰ {exchange_name} API调用超时({api_timeout}秒), 耗时: {call_duration:.3f}秒")
            return None
        except Exception as e:
            call_duration = time.time() - call_start_time
            error_msg = str(e)
            
            # 🔥 使用统一配置的错误处理
            unified_config = getattr(self, 'unified_config', None)
            is_rate_limit_error = False
            if unified_config and hasattr(unified_config, 'is_rate_limit_error'):
                is_rate_limit_error = unified_config.is_rate_limit_error(error_msg)
            else:
                # 备用判断
                is_rate_limit_error = any(err in error_msg for err in ["Too Many Requests", "50011", "10006"])

            # 🔥 更智能的重试机制：限速错误特殊处理
            if is_rate_limit_error:
                error_cooldown = getattr(self, 'cooldown_config', {}).get('error_cooldown', 10.0)
                self.logger.warning(f"⚠️ {exchange_name} 触发限速错误，特殊延迟{error_cooldown}秒: {error_msg}")
                await asyncio.sleep(error_cooldown)

                # 🔥 使用统一配置的重试参数
                max_retries = getattr(self, 'retry_config', {}).get('max_retries', 3)
                exponential_base = getattr(self, 'retry_config', {}).get('exponential_base', 2.0)
                max_retry_delay = getattr(self, 'retry_config', {}).get('max_retry_delay', 60.0)
                
                # 指数退避重试
                for retry_count in range(max_retries):
                    retry_delay = min(exponential_base ** retry_count, max_retry_delay)
                    self.logger.info(f"🔄 {exchange_name} 限速错误重试 {retry_count + 1}/{max_retries}，延迟{retry_delay}秒")
                    await asyncio.sleep(retry_delay)

                    try:
                        # 重新执行调用
                        result = await asyncio.wait_for(func(*args, **kwargs), timeout=api_timeout)
                        self.logger.info(f"✅ {exchange_name} 限速错误重试成功")
                        return result
                    except Exception as retry_e:
                        if retry_count == max_retries - 1:  # 最后一次重试
                            self.logger.error(f"❌ {exchange_name} 限速错误重试失败: {retry_e}")
                        continue
            else:
                self.logger.error(f"❌ {exchange_name} API调用失败 (耗时: {call_duration:.3f}秒): {e}")

            # 🔥 失败的调用也要计入限速，避免因为失败而绕过限速控制
            return None
            
    async def batch_balance_queries(self, exchanges: Dict[str, Any]):
        """批量化余额查询"""
        self.logger.info("💰 批量化余额查询...")

    async def batch_balance_queries(self, exchanges: Dict[str, Any]):
        """🔥 **配置统一化**：批量化余额查询，使用统一配置参数"""
        self.logger.info("💰 批量化余额查询...")
        
        # 🔥 使用统一配置的最大并发数
        max_parallel = getattr(self, 'batch_config', {}).get('max_parallel_requests', 10)
        
        tasks = []
        for exchange_name, exchange in exchanges.items():
            if hasattr(exchange, 'get_balance'):
                task = self.rate_limited_api_call(
                    exchange_name,
                    self._get_balance,
                    exchange
                )
                tasks.append(task)
                
                # 控制并发数量
                if len(tasks) >= max_parallel:
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    success_count = sum(1 for r in results if not isinstance(r, Exception))
                    self.logger.debug(f"💰 余额查询批次完成: {success_count}/{len(tasks)}")
                    tasks = []
        
        # 处理剩余任务
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.debug(f"💰 余额查询最后批次完成: {success_count}/{len(tasks)}")
        
        self.logger.info(f"✅ 余额查询完成: 使用统一配置最大并发{max_parallel}")

    async def smart_contract_info_fetch(self, exchanges: Dict[str, Any], symbols: List[str]):
        """🔥 **配置统一化**：智能化合约信息获取，使用统一配置参数"""
        self.logger.info("📊 智能化合约信息获取...")
        
        # 🔥 使用统一配置的最大并发数和超时设置
        max_parallel = getattr(self, 'batch_config', {}).get('max_parallel_requests', 10)
        timeout = getattr(self, 'timeout_config', {}).get('total_timeout', 45.0)
        
        tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'get_contract_info'):
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._get_contract_info,
                        exchange, symbol
                    )
                    tasks.append(task)
                    
                    # 控制并发数量
                    if len(tasks) >= max_parallel:
                        try:
                            results = await asyncio.wait_for(
                                asyncio.gather(*tasks, return_exceptions=True), 
                                timeout=timeout
                            )
                            success_count = sum(1 for r in results if not isinstance(r, Exception))
                            self.logger.debug(f"📊 合约信息批次完成: {success_count}/{len(tasks)}")
                        except asyncio.TimeoutError:
                            self.logger.warning(f"⚠️ 合约信息获取超时({timeout}秒)")
                        tasks = []
        
        # 处理剩余任务
        if tasks:
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=timeout
                )
                success_count = sum(1 for r in results if not isinstance(r, Exception))
                self.logger.debug(f"📊 合约信息最后批次完成: {success_count}/{len(tasks)}")
            except asyncio.TimeoutError:
                self.logger.warning(f"⚠️ 合约信息最后批次超时({timeout}秒)")
        
        self.logger.info(f"✅ 合约信息获取完成: 使用统一配置最大并发{max_parallel}")

    async def delayed_leverage_setup(self, exchanges: Dict[str, Any], symbols: List[str]):
        """🔥 **配置统一化**：延迟杠杆设置，使用统一配置参数"""
        self.logger.info("🔧 延迟杠杆设置...")

        # 🔥 使用统一配置的延迟时间
        leverage_delay = getattr(self, 'batch_config', {}).get('startup_delay', 1.0)
        await asyncio.sleep(leverage_delay)
        
        # 🔥 使用统一配置的重试参数
        max_retries = getattr(self, 'retry_config', {}).get('max_retries', 3)
        
        tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'set_leverage'):
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._set_leverage,
                        exchange, symbol, 3  # 3倍杠杆
                    )
                    tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        
        # 🔥 记录失败的杠杆设置，使用统一错误处理
        failed_count = len(tasks) - success_count
        if failed_count > 0:
            self.logger.warning(f"⚠️ 杠杆设置部分失败: {failed_count}/{len(tasks)}")
        
        self.logger.info(f"✅ 杠杆设置完成: {success_count}/{len(tasks)}")

    async def _get_trading_rule(self, exchange, symbol: str, market_type: str):
        """获取交易规则"""
        try:
            if hasattr(exchange, 'get_trading_rule'):
                return await exchange.get_trading_rule(symbol, market_type)
            return None
        except Exception as e:
            self.logger.debug(f"获取交易规则失败: {symbol} {market_type} - {e}")
            return None

    async def _get_balance(self, exchange):
        """获取余额"""
        try:
            # 使用现有的统一接口
            if hasattr(exchange, 'get_balance'):
                return await exchange.get_balance()
            return None
        except Exception as e:
            self.logger.debug(f"获取余额失败: {e}")
            return None

    async def _get_contract_info(self, exchange, symbol: str):
        """获取合约信息"""
        try:
            if hasattr(exchange, 'get_contract_info'):
                return await exchange.get_contract_info(symbol)
            return None
        except Exception as e:
            self.logger.debug(f"获取合约信息失败: {symbol} - {e}")
            return None

    async def _set_leverage(self, exchange, symbol: str, leverage: int):
        """设置杠杆"""
        try:
            if hasattr(exchange, 'set_leverage'):
                return await exchange.set_leverage(symbol, leverage)
            return None
        except Exception as e:
            self.logger.debug(f"设置杠杆失败: {symbol} {leverage}x - {e}")
            return None

# 🔥 **根源修复**：集成连接池限速机制，避免重复实现
class UnifiedAPIRateLimiter:
    """🔥 统一API限速管理器 - 集成连接池和交易所限速逻辑"""
    
    def __init__(self):
        self.logger = logger
        
        # 🔥 **配置统一化**：使用统一API配置
        try:
            from config.unified_api_config import get_unified_api_config
            self.unified_config = get_unified_api_config()
            
            # 🔥 从统一配置加载限速配置
            self.unified_rate_limits = {
                exchange: self.unified_config.get_rate_limit(exchange)
                for exchange in ["gate", "bybit", "okx"]
            }
            
            # 🔥 从统一配置加载冷却配置
            self.cooldown_config = self.unified_config.COOLDOWN_CONFIG.copy()
            
            self.logger.info("✅ 统一限速管理器使用统一API配置")
            self.logger.info(f"🔧 统一限速配置: {self.unified_rate_limits}")
            
        except ImportError:
            # 🔥 **兜底配置**
            self.logger.warning("⚠️ 统一API配置不可用，使用默认限速配置")
            
            self.unified_rate_limits = {
                "gate": 8,     # Gate.io: 降低限制确保健壮性
                "bybit": 4,    # Bybit: 降低限制确保健壮性  
                "okx": 2       # OKX: 平衡限速和WebSocket性能
            }
            
            self.cooldown_config = {
                "base_cooldown": 1.5,
                "buffer_cooldown": 3.5,
                "total_cooldown": 5.0
            }
        
        # 🔥 集成连接池管理器的限速逻辑
        self._connection_pool_manager = None
        
    def get_connection_pool_manager(self):
        """懒加载连接池管理器"""
        if self._connection_pool_manager is None:
            try:
                from websocket.unified_connection_pool_manager import get_connection_pool_manager
                self._connection_pool_manager = get_connection_pool_manager()
            except ImportError:
                self.logger.warning("连接池管理器不可用，使用独立限速")
        return self._connection_pool_manager
        
    async def rate_limit_wait(self, exchange_name: str):
        """🔥 统一限速等待方法 - 集成连接池逻辑"""
        exchange_key = exchange_name.lower()
        rate_limit = self.unified_rate_limits.get(exchange_key, 10)
        
        # 🔥 关键修复：使用统一的限速控制逻辑
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_key}_last_call", 0)
        time_since_last = current_time - last_call_time
        
        # 🔥 优化修复：合理的冷却时间，平衡限速和性能
        if exchange_key == "okx":
            min_interval = 0.50  # OKX使用0.5秒间隔支持2次/秒
        else:
            base_interval = 1.0 / rate_limit
            min_interval = max(base_interval, self.cooldown_config["base_cooldown"])
        
        # 🔥 确保严格遵守冷却时间
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.debug(f"🕐 {exchange_key} 统一限速等待: {wait_time:.3f}秒")
            await asyncio.sleep(wait_time)
        
        # 🔥 修复：在等待完成后立即更新时间戳
        setattr(self, f"_{exchange_key}_last_call", time.time())
        
        # 🔥 集成：通知连接池管理器API调用事件
        pool_manager = self.get_connection_pool_manager()
        if pool_manager:
            try:
                # 🔥 修复API限速与连接池的集成
                connection_id = f"{exchange_key}_websocket"
                if hasattr(pool_manager, '_fix_api_rate_limit'):
                    # 如果连接池检测到限速问题，应用修复
                    await pool_manager._fix_api_rate_limit(connection_id)
            except Exception as e:
                self.logger.debug(f"连接池API限速集成异常: {e}")

# 🔥 **统一全局实例管理**
_api_optimizer = None
_unified_rate_limiter = None

def get_api_optimizer():
    """获取API优化器实例 - 🔥 保持向后兼容"""
    global _api_optimizer
    if _api_optimizer is None:
        _api_optimizer = APICallOptimizer()
    return _api_optimizer

def get_unified_rate_limiter():
    """🔥 新增：获取统一限速管理器实例"""
    global _unified_rate_limiter
    if _unified_rate_limiter is None:
        _unified_rate_limiter = UnifiedAPIRateLimiter()
    return _unified_rate_limiter
