"""
统一交易对验证机制
解决SHIB等不支持的交易对问题，实现优雅降级
"""

import json
import os
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from utils.logger import get_logger

logger = get_logger("SymbolValidator")

@dataclass
class ExchangeSymbolMapping:
    """交易所交易对映射"""
    exchange: str
    supported_symbols: Set[str]
    unsupported_symbols: Set[str]  # 明确不支持的交易对
    symbol_format_rules: Dict[str, str]  # 格式转换规则

class UnifiedSymbolValidator:
    """统一交易对验证器"""
    
    def __init__(self):
        self.exchange_mappings = {
            'bybit': ExchangeSymbolMapping(
                exchange='bybit',
                supported_symbols=set(),  # 动态更新
                unsupported_symbols={
                    'SHIBUSD', 'SHIBUSDT',  # SHIB在Bybit没有期货合约
                    'DOGUSD', 'DOGUSDT'     # 部分合约不存在
                },
                symbol_format_rules={
                    'separator': '',  # BTCUSDT
                    'case': 'upper'
                }
            ),
            'okx': ExchangeSymbolMapping(
                exchange='okx',
                supported_symbols=set(),
                unsupported_symbols=set(),
                symbol_format_rules={
                    'separator': '-',  # BTC-USDT
                    'case': 'upper'
                }
            ),
            'gate': ExchangeSymbolMapping(
                exchange='gate',
                supported_symbols=set(),
                unsupported_symbols=set(),
                symbol_format_rules={
                    'separator': '_',  # BTC_USDT
                    'case': 'upper'
                }
            )
        }
        
        self.load_symbol_cache()
    
    def load_symbol_cache(self):
        """加载交易对缓存"""
        cache_file = "/root/myproject/123/70 gate和okx还是数据阻塞/123/cache/symbols_cache.json"
        try:
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                    
                for exchange, data in cache_data.items():
                    if exchange in self.exchange_mappings:
                        self.exchange_mappings[exchange].supported_symbols = set(data.get('supported', []))
                        logger.info(f"✅ 加载{exchange}交易对缓存: {len(self.exchange_mappings[exchange].supported_symbols)}个")
        except Exception as e:
            logger.warning(f"⚠️ 加载交易对缓存失败: {e}")
    
    def format_symbol_for_exchange(self, symbol: str, exchange: str) -> str:
        """为特定交易所格式化交易对 - 🔥 修复版：完善格式转换逻辑"""
        if exchange not in self.exchange_mappings:
            return symbol

        rules = self.exchange_mappings[exchange].symbol_format_rules

        # 🔥 修复：更智能的标准化输入处理
        symbol = symbol.upper().strip()

        # 🔥 修复：处理各种输入格式
        if 'USDT' in symbol:
            # 移除所有分隔符，提取基础货币
            clean_symbol = symbol.replace('-', '').replace('_', '')
            if clean_symbol.endswith('USDT'):
                base = clean_symbol[:-4]  # 移除USDT
                separator = rules.get('separator', '')
                formatted = f"{base}{separator}USDT"
            else:
                formatted = symbol
        elif 'USD' in symbol and not symbol.endswith('USDT'):
            # 处理USD结尾的交易对
            clean_symbol = symbol.replace('-', '').replace('_', '')
            if clean_symbol.endswith('USD'):
                base = clean_symbol[:-3]  # 移除USD
                separator = rules.get('separator', '')
                formatted = f"{base}{separator}USD"
            else:
                formatted = symbol
        else:
            # 🔥 修复：处理其他格式的交易对
            if '-' in symbol or '_' in symbol:
                # 已有分隔符，重新格式化
                parts = symbol.replace('-', '_').split('_')
                if len(parts) == 2:
                    base, quote = parts
                    separator = rules.get('separator', '')
                    formatted = f"{base}{separator}{quote}"
                else:
                    formatted = symbol
            else:
                formatted = symbol

        return formatted.upper() if rules.get('case') == 'upper' else formatted.lower()
    
    def is_symbol_supported(self, symbol: str, exchange: str, market_type: str = 'spot') -> bool:
        """检查交易对是否被交易所支持"""
        if exchange not in self.exchange_mappings:
            return True  # 未知交易所默认支持
        
        mapping = self.exchange_mappings[exchange]
        formatted_symbol = self.format_symbol_for_exchange(symbol, exchange)
        
        # 检查明确不支持列表
        if formatted_symbol in mapping.unsupported_symbols:
            logger.debug(f"🔧 {exchange} 明确不支持: {formatted_symbol}")
            return False
        
        # 特殊规则：Bybit期货市场的SHIB检查
        if exchange == 'bybit' and market_type == 'futures':
            if 'SHIB' in formatted_symbol.upper():
                logger.debug(f"🔧 Bybit期货不支持SHIB: {formatted_symbol}")
                return False
        
        # 如果有支持列表且不为空，检查是否在支持列表中
        if mapping.supported_symbols and formatted_symbol not in mapping.supported_symbols:
            logger.debug(f"🔧 {exchange} 支持列表中未找到: {formatted_symbol}")
            return False
        
        return True
    
    def filter_supported_symbols(self, symbols: List[str], exchange: str, market_type: str = 'spot') -> List[str]:
        """过滤出交易所支持的交易对"""
        supported = []
        filtered_out = []
        
        for symbol in symbols:
            if self.is_symbol_supported(symbol, exchange, market_type):
                formatted = self.format_symbol_for_exchange(symbol, exchange)
                supported.append(formatted)
            else:
                filtered_out.append(symbol)
        
        if filtered_out:
            logger.info(f"🔧 {exchange} 智能过滤掉 {len(filtered_out)} 个不支持的交易对: {filtered_out}")
        
        logger.info(f"✅ {exchange} {market_type} 支持 {len(supported)} 个交易对: {supported}")
        return supported
    
    def get_exchange_specific_unsupported_symbols(self, exchange: str) -> Set[str]:
        """获取交易所特定的不支持交易对"""
        if exchange in self.exchange_mappings:
            return self.exchange_mappings[exchange].unsupported_symbols.copy()
        return set()
    
    def add_unsupported_symbol(self, symbol: str, exchange: str):
        """动态添加不支持的交易对"""
        if exchange in self.exchange_mappings:
            formatted = self.format_symbol_for_exchange(symbol, exchange)
            self.exchange_mappings[exchange].unsupported_symbols.add(formatted)
            logger.info(f"📝 {exchange} 添加不支持交易对: {formatted}")
    
    def normalize_symbol(self, symbol: str, exchange: str) -> str:
        """标准化交易对格式 - 为兼容性测试提供的方法"""
        return self.format_symbol_for_exchange(symbol, exchange)
    
    def update_supported_symbols(self, symbols: List[str], exchange: str):
        """更新交易所支持的交易对列表"""
        if exchange in self.exchange_mappings:
            formatted_symbols = [self.format_symbol_for_exchange(s, exchange) for s in symbols]
            self.exchange_mappings[exchange].supported_symbols = set(formatted_symbols)
            logger.info(f"📝 {exchange} 更新支持交易对: {len(formatted_symbols)}个")

# 全局实例
_symbol_validator = None

def get_symbol_validator() -> UnifiedSymbolValidator:
    """获取全局统一交易对验证器实例"""
    global _symbol_validator
    if _symbol_validator is None:
        _symbol_validator = UnifiedSymbolValidator()
    return _symbol_validator