#!/usr/bin/env python3
"""
🔥 综合系统诊断脚本 - 精确定位核心问题
基于修复提示词的8个内部检查清单进行全面诊断
"""

import asyncio
import json
import time
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger

logger = get_logger("SystemDiagnosis")

class ComprehensiveSystemDiagnosis:
    """综合系统诊断器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'diagnosis_summary': {},
            'critical_issues': [],
            'high_priority_issues': [],
            'medium_priority_issues': [],
            'architecture_analysis': {},
            'unified_modules_status': {},
            'websocket_health': {},
            'api_optimization_status': {},
            'recommendations': []
        }
        
    async def run_comprehensive_diagnosis(self):
        """运行综合诊断"""
        logger.info("🚀 开始综合系统诊断...")
        
        # 1. 检查现有架构中的功能
        await self.check_existing_architecture()
        
        # 2. 验证统一模块使用情况
        await self.verify_unified_modules_usage()
        
        # 3. 精确定位问题根本原因
        await self.identify_root_causes()
        
        # 4. 检查链路和接口
        await self.check_interfaces_and_chains()
        
        # 5. 验证三交易所一致性
        await self.verify_exchange_consistency()
        
        # 6. 检查重复调用和造轮子问题
        await self.check_code_duplication()
        
        # 7. WebSocket数据流阻塞诊断
        await self.diagnose_websocket_blocking()
        
        # 8. API限速问题诊断
        await self.diagnose_api_rate_limiting()
        
        # 生成诊断报告
        await self.generate_diagnosis_report()
        
        return self.results
        
    async def check_existing_architecture(self):
        """检查现有架构中是否已有功能"""
        logger.info("🔍 检查现有架构...")
        
        architecture_status = {
            'unified_modules_count': 0,
            'websocket_modules': [],
            'core_modules': [],
            'missing_modules': [],
            'duplicate_implementations': []
        }
        
        try:
            # 检查统一模块
            unified_modules = [
                'unified_timestamp_processor',
                'unified_connection_pool_manager', 
                'unified_data_formatter',
                'unified_order_spread_calculator',
                'unified_balance_manager',
                'unified_depth_analyzer'
            ]
            
            for module in unified_modules:
                try:
                    module_path = f"websocket/{module}.py"
                    if os.path.exists(module_path):
                        architecture_status['unified_modules_count'] += 1
                        architecture_status['websocket_modules'].append(module)
                    else:
                        # 检查其他目录
                        for dir_name in ['core', 'utils', 'trading']:
                            alt_path = f"{dir_name}/{module}.py"
                            if os.path.exists(alt_path):
                                architecture_status['unified_modules_count'] += 1
                                architecture_status['core_modules'].append(f"{dir_name}/{module}")
                                break
                        else:
                            architecture_status['missing_modules'].append(module)
                except Exception as e:
                    logger.error(f"检查模块{module}失败: {e}")
                    
            self.results['architecture_analysis'] = architecture_status
            
        except Exception as e:
            logger.error(f"架构检查失败: {e}")
            self.results['critical_issues'].append({
                'issue': '架构检查失败',
                'description': str(e),
                'severity': 'CRITICAL'
            })
            
    async def verify_unified_modules_usage(self):
        """验证统一模块使用情况"""
        logger.info("🔍 验证统一模块使用情况...")
        
        usage_analysis = {
            'websocket_implementations': {},
            'import_patterns': {},
            'consistency_issues': []
        }
        
        try:
            # 检查WebSocket实现
            websocket_files = ['gate_ws.py', 'bybit_ws.py', 'okx_ws.py']
            
            for ws_file in websocket_files:
                file_path = f"websocket/{ws_file}"
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    analysis = {
                        'uses_unified_timestamp': 'unified_timestamp_processor' in content,
                        'uses_connection_pool': 'unified_connection_pool_manager' in content,
                        'has_monitor_data_flow': '_monitor_data_flow' in content,
                        'has_handle_blocking': '_handle_data_flow_blocking' in content,
                        'import_style': 'global' if 'get_synced_timestamp(' in content else 'instance'
                    }
                    
                    usage_analysis['websocket_implementations'][ws_file] = analysis
                    
                    # 检查一致性问题
                    if analysis['has_monitor_data_flow']:
                        usage_analysis['consistency_issues'].append({
                            'file': ws_file,
                            'issue': 'WebSocket并发冲突风险',
                            'description': '存在_monitor_data_flow方法可能导致并发冲突'
                        })
                        
            self.results['unified_modules_status'] = usage_analysis
            
        except Exception as e:
            logger.error(f"统一模块验证失败: {e}")
            
    async def identify_root_causes(self):
        """精确定位问题根本原因"""
        logger.info("🔍 精确定位问题根本原因...")
        
        root_causes = []
        
        try:
            # 1. WebSocket数据流阻塞问题
            okx_ws_path = "websocket/okx_ws.py"
            if os.path.exists(okx_ws_path):
                with open(okx_ws_path, 'r', encoding='utf-8') as f:
                    okx_content = f.read()
                    
                # 🔥 精确检测：只检查实际的方法定义，不包括注释
                if 'def _monitor_data_flow(' in okx_content or 'async def _monitor_data_flow(' in okx_content:
                    root_causes.append({
                        'issue': 'OKX WebSocket并发冲突',
                        'root_cause': '_monitor_data_flow()监控任务与主消息循环产生并发冲突',
                        'evidence': 'okx_ws.py中存在_monitor_data_flow方法定义',
                        'severity': 'CRITICAL',
                        'impact': '导致286次WebSocket并发错误，数据流阻塞30秒以上'
                    })
                elif 'asyncio.gather(' in okx_content and 'subscription_tasks' in okx_content:
                    root_causes.append({
                        'issue': 'OKX WebSocket订阅并发风险',
                        'root_cause': '订阅阶段使用asyncio.gather可能与主消息循环冲突',
                        'evidence': 'okx_ws.py中存在asyncio.gather并行订阅',
                        'severity': 'HIGH',
                        'impact': '可能导致WebSocket连接不稳定'
                    })
                    
            # 2. 时间戳处理不一致问题
            bybit_ws_path = "websocket/bybit_ws.py"
            if os.path.exists(bybit_ws_path):
                with open(bybit_ws_path, 'r', encoding='utf-8') as f:
                    bybit_content = f.read()
                    
                if 'get_synced_timestamp("bybit"' in bybit_content:
                    root_causes.append({
                        'issue': 'Bybit时间戳处理不一致',
                        'root_cause': '使用全局函数调用绕过数据新鲜度检查',
                        'evidence': 'bybit_ws.py中使用get_synced_timestamp("bybit", data)全局调用',
                        'severity': 'HIGH',
                        'impact': '导致三交易所时间戳处理不统一，影响监控一致性'
                    })
                    
            # 3. API限速问题
            api_optimizer_path = "core/api_call_optimizer.py"
            if os.path.exists(api_optimizer_path):
                with open(api_optimizer_path, 'r', encoding='utf-8') as f:
                    api_content = f.read()
                    
                if '"okx": 1' in api_content or '"okx": 2' in api_content:
                    root_causes.append({
                        'issue': 'OKX API限速过于严格',
                        'root_cause': 'API限速设置过低导致WebSocket连接被拒绝',
                        'evidence': 'api_call_optimizer.py中OKX限速设置过低',
                        'severity': 'HIGH',
                        'impact': '导致HTTP 503错误，WebSocket连接失败'
                    })
                    
            self.results['critical_issues'].extend([r for r in root_causes if r['severity'] == 'CRITICAL'])
            self.results['high_priority_issues'].extend([r for r in root_causes if r['severity'] == 'HIGH'])
            
        except Exception as e:
            logger.error(f"根本原因分析失败: {e}")
            
    async def check_interfaces_and_chains(self):
        """检查链路和接口"""
        logger.info("🔍 检查链路和接口...")
        
        interface_analysis = {
            'websocket_chain': {},
            'timestamp_chain': {},
            'api_chain': {},
            'broken_chains': []
        }
        
        try:
            # 检查WebSocket链路
            ws_manager_path = "websocket/ws_manager.py"
            if os.path.exists(ws_manager_path):
                with open(ws_manager_path, 'r', encoding='utf-8') as f:
                    ws_manager_content = f.read()
                    
                interface_analysis['websocket_chain'] = {
                    'has_manager': True,
                    'manages_connections': 'start_client' in ws_manager_content,
                    'handles_errors': 'error_handler' in ws_manager_content,
                    'monitors_performance': 'performance_monitor' in ws_manager_content
                }
                
            # 检查时间戳处理链路
            timestamp_processor_path = "websocket/unified_timestamp_processor.py"
            if os.path.exists(timestamp_processor_path):
                with open(timestamp_processor_path, 'r', encoding='utf-8') as f:
                    timestamp_content = f.read()
                    
                interface_analysis['timestamp_chain'] = {
                    'has_processor': True,
                    'extracts_server_timestamp': '_extract_server_timestamp_for_monitoring' in timestamp_content,
                    'validates_freshness': 'max_age_ms' in timestamp_content,
                    'syncs_across_exchanges': 'check_cross_exchange_sync' in timestamp_content
                }
                
            self.results['architecture_analysis']['interface_analysis'] = interface_analysis
            
        except Exception as e:
            logger.error(f"接口链路检查失败: {e}")

    async def verify_exchange_consistency(self):
        """验证三交易所一致性"""
        logger.info("🔍 验证三交易所一致性...")

        consistency_analysis = {
            'websocket_implementations': {},
            'timestamp_processing': {},
            'api_handling': {},
            'inconsistencies': []
        }

        try:
            exchanges = ['gate', 'bybit', 'okx']

            for exchange in exchanges:
                ws_file = f"websocket/{exchange}_ws.py"
                exchange_file = f"exchanges/{exchange}_exchange.py"

                analysis = {
                    'websocket_exists': os.path.exists(ws_file),
                    'exchange_exists': os.path.exists(exchange_file),
                    'timestamp_method': 'unknown',
                    'has_monitoring': False,
                    'heartbeat_interval': 'unknown'
                }

                if os.path.exists(ws_file):
                    with open(ws_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查时间戳处理方式
                    if 'self.timestamp_processor.get_synced_timestamp' in content:
                        analysis['timestamp_method'] = 'instance'
                    elif 'get_synced_timestamp(' in content:
                        analysis['timestamp_method'] = 'global'

                    # 检查监控机制
                    analysis['has_monitoring'] = '_monitor_data_flow' in content

                    # 检查心跳间隔
                    if 'heartbeat_interval = 5' in content:
                        analysis['heartbeat_interval'] = 5
                    elif 'heartbeat_interval = 20' in content:
                        analysis['heartbeat_interval'] = 20
                    elif 'heartbeat_interval = 15' in content:
                        analysis['heartbeat_interval'] = 15

                consistency_analysis['websocket_implementations'][exchange] = analysis

            # 检查不一致性
            timestamp_methods = set()
            heartbeat_intervals = set()
            monitoring_status = set()

            for exchange, data in consistency_analysis['websocket_implementations'].items():
                timestamp_methods.add(data['timestamp_method'])
                if data['heartbeat_interval'] != 'unknown':
                    heartbeat_intervals.add(data['heartbeat_interval'])
                monitoring_status.add(data['has_monitoring'])

            if len(timestamp_methods) > 1:
                consistency_analysis['inconsistencies'].append({
                    'type': '时间戳处理方式不一致',
                    'details': f"发现{len(timestamp_methods)}种不同的时间戳处理方式: {timestamp_methods}",
                    'severity': 'HIGH'
                })

            if len(heartbeat_intervals) > 1:
                consistency_analysis['inconsistencies'].append({
                    'type': '心跳间隔不一致',
                    'details': f"发现{len(heartbeat_intervals)}种不同的心跳间隔: {heartbeat_intervals}",
                    'severity': 'MEDIUM'
                })

            if len(monitoring_status) > 1:
                consistency_analysis['inconsistencies'].append({
                    'type': '监控机制不一致',
                    'details': f"部分交易所有监控机制，部分没有: {monitoring_status}",
                    'severity': 'HIGH'
                })

            self.results['architecture_analysis']['consistency_analysis'] = consistency_analysis

        except Exception as e:
            logger.error(f"一致性验证失败: {e}")

    async def check_code_duplication(self):
        """检查重复调用和造轮子问题"""
        logger.info("🔍 检查重复调用和造轮子问题...")

        duplication_analysis = {
            'duplicate_functions': [],
            'redundant_imports': [],
            'similar_implementations': [],
            'optimization_opportunities': []
        }

        try:
            # 检查时间戳处理重复
            timestamp_implementations = []

            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            if 'def get_synced_timestamp' in content:
                                timestamp_implementations.append(file_path)

                            if 'def calculate_data_age' in content:
                                duplication_analysis['duplicate_functions'].append({
                                    'function': 'calculate_data_age',
                                    'file': file_path,
                                    'type': '数据年龄计算重复实现'
                                })

                        except Exception:
                            continue

            if len(timestamp_implementations) > 1:
                duplication_analysis['duplicate_functions'].append({
                    'function': 'get_synced_timestamp',
                    'files': timestamp_implementations,
                    'type': '时间戳同步重复实现'
                })

            self.results['architecture_analysis']['duplication_analysis'] = duplication_analysis

        except Exception as e:
            logger.error(f"重复检查失败: {e}")

    async def diagnose_websocket_blocking(self):
        """WebSocket数据流阻塞诊断"""
        logger.info("🔍 WebSocket数据流阻塞诊断...")

        blocking_analysis = {
            'okx_issues': [],
            'gate_issues': [],
            'bybit_status': [],
            'blocking_patterns': []
        }

        try:
            # 检查OKX WebSocket实现
            okx_ws_path = "websocket/okx_ws.py"
            if os.path.exists(okx_ws_path):
                with open(okx_ws_path, 'r', encoding='utf-8') as f:
                    okx_content = f.read()

                # 检查并发冲突源
                if '_monitor_data_flow' in okx_content:
                    blocking_analysis['okx_issues'].append({
                        'issue': 'WebSocket并发冲突',
                        'description': '存在_monitor_data_flow()监控任务与主消息循环冲突',
                        'severity': 'CRITICAL',
                        'line_evidence': '_monitor_data_flow' in okx_content
                    })

                if '_handle_data_flow_blocking' in okx_content:
                    blocking_analysis['okx_issues'].append({
                        'issue': '数据流阻塞处理',
                        'description': '存在_handle_data_flow_blocking()方法',
                        'severity': 'HIGH',
                        'line_evidence': '_handle_data_flow_blocking' in okx_content
                    })

                # 检查pong响应处理
                if 'if data == "pong"' not in okx_content:
                    blocking_analysis['okx_issues'].append({
                        'issue': 'pong响应处理缺失',
                        'description': 'OKX WebSocket缺少pong响应处理，可能导致30秒超时断开',
                        'severity': 'HIGH',
                        'line_evidence': 'pong响应处理代码缺失'
                    })

            # 检查Gate.io配置问题
            gate_ws_path = "websocket/gate_ws.py"
            if os.path.exists(gate_ws_path):
                with open(gate_ws_path, 'r', encoding='utf-8') as f:
                    gate_content = f.read()

                # 检查心跳间隔配置
                if 'heartbeat_interval = 5' in gate_content:
                    blocking_analysis['gate_issues'].append({
                        'issue': '心跳间隔配置不一致',
                        'description': '局部配置5秒与全局配置20秒不一致',
                        'severity': 'MEDIUM',
                        'line_evidence': 'heartbeat_interval = 5'
                    })

                # 检查消息处理限流
                if 'self._last_orderbook_time < 0.1' in gate_content:
                    blocking_analysis['gate_issues'].append({
                        'issue': '消息处理过度限流',
                        'description': '100ms限流可能导致重要订单簿更新丢失',
                        'severity': 'MEDIUM',
                        'line_evidence': '_last_orderbook_time < 0.1'
                    })

            # 检查Bybit实现（作为正确参考）
            bybit_ws_path = "websocket/bybit_ws.py"
            if os.path.exists(bybit_ws_path):
                with open(bybit_ws_path, 'r', encoding='utf-8') as f:
                    bybit_content = f.read()

                bybit_analysis = {
                    'has_monitoring': '_monitor_data_flow' in bybit_content,
                    'uses_unified_timestamp': 'self.timestamp_processor' in bybit_content,
                    'heartbeat_compliant': 'heartbeat_interval = 20' in bybit_content,
                    'status': 'COMPLIANT' if '_monitor_data_flow' not in bybit_content else 'NON_COMPLIANT'
                }

                blocking_analysis['bybit_status'].append(bybit_analysis)

            self.results['websocket_health'] = blocking_analysis

        except Exception as e:
            logger.error(f"WebSocket阻塞诊断失败: {e}")

    async def diagnose_api_rate_limiting(self):
        """API限速问题诊断"""
        logger.info("🔍 API限速问题诊断...")

        api_analysis = {
            'rate_limit_settings': {},
            'optimization_issues': [],
            'recommendations': []
        }

        try:
            # 检查API优化器配置
            api_optimizer_path = "core/api_call_optimizer.py"
            if os.path.exists(api_optimizer_path):
                with open(api_optimizer_path, 'r', encoding='utf-8') as f:
                    api_content = f.read()

                # 提取限速配置
                import re
                rate_limits = re.findall(r'"(\w+)":\s*(\d+)', api_content)
                for exchange, limit in rate_limits:
                    api_analysis['rate_limit_settings'][exchange] = int(limit)

                # 检查是否过于严格
                for exchange, limit in api_analysis['rate_limit_settings'].items():
                    if exchange == 'okx' and limit < 3:
                        api_analysis['optimization_issues'].append({
                            'exchange': exchange,
                            'issue': 'API限速过于严格',
                            'current_limit': limit,
                            'recommended_limit': 3,
                            'impact': '可能导致WebSocket连接被拒绝'
                        })

            self.results['api_optimization_status'] = api_analysis

        except Exception as e:
            logger.error(f"API限速诊断失败: {e}")

    async def generate_diagnosis_report(self):
        """生成诊断报告"""
        logger.info("📊 生成诊断报告...")

        # 统计问题数量
        critical_count = len(self.results['critical_issues'])
        high_count = len(self.results['high_priority_issues'])
        medium_count = len(self.results['medium_priority_issues'])

        # 生成修复建议
        recommendations = []

        if critical_count > 0:
            recommendations.append({
                'priority': 'CRITICAL',
                'action': '立即修复WebSocket并发冲突问题',
                'details': '移除OKX WebSocket中的_monitor_data_flow()方法，采用Bybit简洁架构'
            })

        if high_count > 0:
            recommendations.append({
                'priority': 'HIGH',
                'action': '统一三交易所时间戳处理方式',
                'details': '将Bybit改为使用实例方法调用，确保三交易所一致性'
            })

        # 生成摘要
        self.results['diagnosis_summary'] = {
            'total_issues': critical_count + high_count + medium_count,
            'critical_issues': critical_count,
            'high_priority_issues': high_count,
            'medium_priority_issues': medium_count,
            'overall_health': 'CRITICAL' if critical_count > 0 else 'WARNING' if high_count > 0 else 'GOOD',
            'unified_modules_found': self.results['architecture_analysis'].get('unified_modules_count', 0),
            'consistency_issues': len(self.results['architecture_analysis'].get('consistency_analysis', {}).get('inconsistencies', [])),
            'recommendations_count': len(recommendations)
        }

        self.results['recommendations'] = recommendations

        # 输出诊断结果
        logger.info("=" * 80)
        logger.info("🎯 综合系统诊断结果")
        logger.info("=" * 80)
        logger.info(f"📊 问题统计: 严重{critical_count}个, 高优先级{high_count}个, 中等{medium_count}个")
        logger.info(f"🏗️ 统一模块: 发现{self.results['diagnosis_summary']['unified_modules_found']}个")
        logger.info(f"🔗 一致性问题: {self.results['diagnosis_summary']['consistency_issues']}个")
        logger.info(f"💡 修复建议: {len(recommendations)}条")
        logger.info(f"🎯 整体健康度: {self.results['diagnosis_summary']['overall_health']}")

        if critical_count > 0:
            logger.error("🚨 发现严重问题，需要立即修复！")
            for issue in self.results['critical_issues']:
                logger.error(f"  - {issue['issue']}: {issue['root_cause']}")

        return self.results


async def main():
    """主函数"""
    try:
        diagnosis = ComprehensiveSystemDiagnosis()
        results = await diagnosis.run_comprehensive_diagnosis()

        # 保存诊断结果
        output_file = f"diagnostic_scripts/system_diagnosis_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        logger.info(f"📄 诊断结果已保存到: {output_file}")

        return results

    except Exception as e:
        logger.error(f"诊断失败: {e}")
        return None


if __name__ == "__main__":
    asyncio.run(main())
