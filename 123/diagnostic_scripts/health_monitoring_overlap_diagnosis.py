#!/usr/bin/env python3
"""
健康监控和连接池管理功能重叠问题精确诊断脚本
精准定位重复逻辑，分析功能重叠，提供统一修复方案
"""

import os
import sys
import json
import time
import re
from pathlib import Path
from typing import Dict, List, Any, Set

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class HealthMonitoringOverlapDiagnosis:
    """健康监控功能重叠诊断器"""
    
    def __init__(self):
        self.project_root = project_root
        self.diagnosis_results = {
            "timestamp": int(time.time()),
            "diagnosis_type": "健康监控和连接池管理功能重叠分析",
            "overlap_analysis": {},
            "duplicate_functions": [],
            "redundant_logic": [],
            "integration_opportunities": [],
            "api_compliance_check": {},
            "optimization_recommendations": []
        }
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始健康监控功能重叠诊断...")
        print("=" * 80)
        
        # 1. 分析健康监控功能分布
        self._analyze_health_monitoring_distribution()
        
        # 2. 检测功能重叠
        self._detect_function_overlap()
        
        # 3. 分析重复逻辑
        self._analyze_redundant_logic()
        
        # 4. 检查API规则符合性
        self._check_api_compliance()
        
        # 5. 生成优化建议
        self._generate_optimization_recommendations()
        
        # 6. 保存诊断结果
        self._save_diagnosis_results()
        
        return self.diagnosis_results
    
    def _analyze_health_monitoring_distribution(self):
        """分析健康监控功能分布"""
        print("📊 分析健康监控功能分布...")
        
        health_monitoring_files = {
            "system_monitor.py": {"path": "core/system_monitor.py", "functions": []},
            "ws_client.py": {"path": "websocket/ws_client.py", "functions": []},
            "ws_manager.py": {"path": "websocket/ws_manager.py", "functions": []},
            "unified_connection_pool_manager.py": {"path": "websocket/unified_connection_pool_manager.py", "functions": []},
            "performance_monitor.py": {"path": "websocket/performance_monitor.py", "functions": []}
        }
        
        # 健康监控相关的函数模式
        health_patterns = [
            r'def.*health.*check',
            r'def.*monitor.*health',
            r'def.*check.*health',
            r'async def.*health',
            r'def.*_monitor_',
            r'async def.*_monitor_',
            r'def.*connection.*check',
            r'async def.*connection.*check'
        ]
        
        for file_key, file_info in health_monitoring_files.items():
            file_path = self.project_root / file_info["path"]
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找健康监控相关函数
                for pattern in health_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        file_info["functions"].append({
                            "function": match,
                            "pattern": pattern,
                            "type": self._classify_health_function(match)
                        })
                
                # 统计行数和复杂度
                lines = content.split('\n')
                health_lines = [line for line in lines if any(keyword in line.lower() for keyword in 
                               ['health', 'monitor', 'check', 'status', 'connection'])]
                
                file_info["total_lines"] = len(lines)
                file_info["health_related_lines"] = len(health_lines)
                file_info["health_percentage"] = (len(health_lines) / len(lines)) * 100 if lines else 0
        
        self.diagnosis_results["overlap_analysis"]["file_distribution"] = health_monitoring_files
        
        # 统计总体分布
        total_functions = sum(len(info["functions"]) for info in health_monitoring_files.values())
        print(f"   📋 发现健康监控相关函数: {total_functions}个")
        
        for file_key, info in health_monitoring_files.items():
            if info["functions"]:
                print(f"   - {file_key}: {len(info['functions'])}个函数, {info['health_percentage']:.1f}%相关代码")
    
    def _classify_health_function(self, function_name: str) -> str:
        """分类健康监控函数"""
        function_lower = function_name.lower()
        
        if 'connection' in function_lower:
            return 'connection_health'
        elif 'websocket' in function_lower or 'ws' in function_lower:
            return 'websocket_health'
        elif 'pool' in function_lower:
            return 'pool_health'
        elif 'system' in function_lower:
            return 'system_health'
        elif 'performance' in function_lower:
            return 'performance_health'
        else:
            return 'general_health'
    
    def _detect_function_overlap(self):
        """检测功能重叠"""
        print("🔍 检测功能重叠...")
        
        # 定义重叠检测规则
        overlap_patterns = {
            "connection_status_check": [
                "检查连接状态",
                "connection.status",
                "ws.open",
                "ConnectionStatus.CONNECTED"
            ],
            "health_check_execution": [
                "健康检查",
                "health_check",
                "send_heartbeat",
                "ping"
            ],
            "reconnection_logic": [
                "重连",
                "reconnect",
                "_reconnect",
                "重新连接"
            ],
            "error_recovery": [
                "错误恢复",
                "error_recovery",
                "handle_connection_issue",
                "修复"
            ]
        }
        
        overlaps = {}
        
        # 检查每个文件中的重叠模式
        for pattern_name, keywords in overlap_patterns.items():
            overlaps[pattern_name] = {"files": [], "total_occurrences": 0}
            
            for file_name in ["system_monitor.py", "ws_client.py", "ws_manager.py", 
                             "unified_connection_pool_manager.py", "performance_monitor.py"]:
                
                if file_name == "system_monitor.py":
                    file_path = self.project_root / "core" / file_name
                else:
                    file_path = self.project_root / "websocket" / file_name
                
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    occurrences = 0
                    found_keywords = []
                    
                    for keyword in keywords:
                        count = content.lower().count(keyword.lower())
                        if count > 0:
                            occurrences += count
                            found_keywords.append(keyword)
                    
                    if occurrences > 0:
                        overlaps[pattern_name]["files"].append({
                            "file": file_name,
                            "occurrences": occurrences,
                            "keywords": found_keywords
                        })
                        overlaps[pattern_name]["total_occurrences"] += occurrences
        
        # 识别重复功能
        duplicate_functions = []
        for pattern_name, data in overlaps.items():
            if len(data["files"]) > 1:  # 多个文件实现相同功能
                duplicate_functions.append({
                    "pattern": pattern_name,
                    "files_count": len(data["files"]),
                    "total_occurrences": data["total_occurrences"],
                    "files": data["files"]
                })
        
        self.diagnosis_results["duplicate_functions"] = duplicate_functions
        self.diagnosis_results["overlap_analysis"]["pattern_analysis"] = overlaps
        
        print(f"   ⚠️ 发现重复功能模式: {len(duplicate_functions)}个")
        for dup in duplicate_functions:
            print(f"   - {dup['pattern']}: {dup['files_count']}个文件实现")
    
    def _analyze_redundant_logic(self):
        """分析重复逻辑"""
        print("🔧 分析重复逻辑...")
        
        redundant_logic = []
        
        # 检查具体的重复逻辑实例
        redundancy_checks = [
            {
                "name": "WebSocket连接状态检查",
                "files": ["ws_client.py", "ws_manager.py", "unified_connection_pool_manager.py"],
                "patterns": ["self.ws.open", "connection.status", "ConnectionStatus.CONNECTED"]
            },
            {
                "name": "健康检查触发逻辑",
                "files": ["ws_client.py", "system_monitor.py", "unified_connection_pool_manager.py"],
                "patterns": ["health_check", "silent_duration", "last_message_time"]
            },
            {
                "name": "重连逻辑",
                "files": ["ws_client.py", "ws_manager.py", "unified_connection_pool_manager.py"],
                "patterns": ["reconnect", "_reconnect", "重新连接"]
            },
            {
                "name": "错误处理和恢复",
                "files": ["ws_client.py", "unified_connection_pool_manager.py", "system_monitor.py"],
                "patterns": ["handle_connection_issue", "auto_fix", "error_recovery"]
            }
        ]
        
        for check in redundancy_checks:
            redundancy_data = {
                "name": check["name"],
                "implementations": [],
                "redundancy_score": 0
            }
            
            for file_name in check["files"]:
                if file_name == "system_monitor.py":
                    file_path = self.project_root / "core" / file_name
                else:
                    file_path = self.project_root / "websocket" / file_name
                
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    pattern_matches = 0
                    matched_patterns = []
                    
                    for pattern in check["patterns"]:
                        if pattern.lower() in content.lower():
                            pattern_matches += 1
                            matched_patterns.append(pattern)
                    
                    if pattern_matches > 0:
                        redundancy_data["implementations"].append({
                            "file": file_name,
                            "matched_patterns": matched_patterns,
                            "pattern_count": pattern_matches
                        })
            
            # 计算冗余分数
            if len(redundancy_data["implementations"]) > 1:
                redundancy_data["redundancy_score"] = len(redundancy_data["implementations"]) * 10
                redundant_logic.append(redundancy_data)
        
        self.diagnosis_results["redundant_logic"] = redundant_logic
        
        print(f"   🔄 发现重复逻辑: {len(redundant_logic)}个")
        for logic in redundant_logic:
            print(f"   - {logic['name']}: {len(logic['implementations'])}个实现, 冗余分数{logic['redundancy_score']}")
    
    def _check_api_compliance(self):
        """检查API规则符合性"""
        print("📋 检查API规则符合性...")
        
        api_compliance = {
            "gate_io": {"compliant": True, "issues": []},
            "bybit": {"compliant": True, "issues": []},
            "okx": {"compliant": True, "issues": []}
        }
        
        # 检查每个交易所的WebSocket实现
        for exchange in ["gate", "bybit", "okx"]:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                exchange_key = exchange if exchange != "gate" else "gate_io"
                
                # 检查健康监控是否符合交易所API规范
                compliance_checks = {
                    "heartbeat_implementation": "heartbeat" in content.lower() or "ping" in content.lower(),
                    "error_handling": "error" in content.lower() and "handle" in content.lower(),
                    "reconnection_logic": "reconnect" in content.lower(),
                    "rate_limiting": "rate" in content.lower() or "limit" in content.lower()
                }
                
                for check_name, passed in compliance_checks.items():
                    if not passed:
                        api_compliance[exchange_key]["issues"].append(f"缺少{check_name}")
                        api_compliance[exchange_key]["compliant"] = False
        
        self.diagnosis_results["api_compliance_check"] = api_compliance
        
        compliant_count = sum(1 for comp in api_compliance.values() if comp["compliant"])
        print(f"   ✅ API规则符合性: {compliant_count}/3个交易所符合")
    
    def _generate_optimization_recommendations(self):
        """生成优化建议"""
        print("💡 生成优化建议...")
        
        recommendations = []
        
        # 基于重复功能生成建议
        duplicate_functions = self.diagnosis_results["duplicate_functions"]
        if duplicate_functions:
            recommendations.append({
                "priority": "HIGH",
                "category": "功能整合",
                "title": "统一健康监控功能",
                "description": f"发现{len(duplicate_functions)}个重复功能模式，建议整合到统一连接池管理器",
                "actions": [
                    "将ws_client.py中的_active_health_monitoring完全委托给连接池管理器",
                    "移除ws_manager.py中的重复健康检查逻辑",
                    "统一system_monitor.py中的WebSocket健康检查接口"
                ]
            })
        
        # 基于重复逻辑生成建议
        redundant_logic = self.diagnosis_results["redundant_logic"]
        high_redundancy = [logic for logic in redundant_logic if logic["redundancy_score"] > 20]
        if high_redundancy:
            recommendations.append({
                "priority": "MEDIUM",
                "category": "逻辑优化",
                "title": "消除重复逻辑",
                "description": f"发现{len(high_redundancy)}个高冗余逻辑，需要重构",
                "actions": [
                    "创建统一的连接状态检查接口",
                    "实施单一职责的健康检查策略",
                    "建立统一的错误恢复机制"
                ]
            })
        
        # 基于API符合性生成建议
        api_compliance = self.diagnosis_results["api_compliance_check"]
        non_compliant = [ex for ex, comp in api_compliance.items() if not comp["compliant"]]
        if non_compliant:
            recommendations.append({
                "priority": "HIGH",
                "category": "API符合性",
                "title": "修复API规则符合性问题",
                "description": f"{len(non_compliant)}个交易所存在API符合性问题",
                "actions": [
                    f"修复{', '.join(non_compliant)}的API符合性问题",
                    "确保所有交易所都实现标准的健康监控接口",
                    "统一错误处理和重连逻辑"
                ]
            })
        
        self.diagnosis_results["optimization_recommendations"] = recommendations
        
        print(f"   📝 生成优化建议: {len(recommendations)}条")
        for rec in recommendations:
            print(f"   - [{rec['priority']}] {rec['title']}")
    
    def _save_diagnosis_results(self):
        """保存诊断结果"""
        timestamp = int(time.time())
        filename = f"health_monitoring_overlap_diagnosis_{timestamp}.json"
        filepath = self.project_root / "diagnostic_scripts" / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 诊断结果已保存到: diagnostic_scripts/{filename}")
    
    def print_summary(self):
        """打印诊断摘要"""
        print("\n" + "="*80)
        print("🔍 健康监控功能重叠诊断摘要")
        print("="*80)
        
        # 重复功能统计
        duplicate_count = len(self.diagnosis_results["duplicate_functions"])
        redundant_count = len(self.diagnosis_results["redundant_logic"])
        
        print(f"📊 功能重叠分析:")
        print(f"   - 重复功能模式: {duplicate_count}个")
        print(f"   - 重复逻辑实例: {redundant_count}个")
        
        # API符合性统计
        api_compliance = self.diagnosis_results["api_compliance_check"]
        compliant_count = sum(1 for comp in api_compliance.values() if comp["compliant"])
        print(f"   - API规则符合性: {compliant_count}/3个交易所")
        
        # 优化建议统计
        recommendations = self.diagnosis_results["optimization_recommendations"]
        high_priority = len([r for r in recommendations if r["priority"] == "HIGH"])
        print(f"   - 优化建议: {len(recommendations)}条 (高优先级: {high_priority}条)")
        
        # 关键发现
        print(f"\n🎯 关键发现:")
        if duplicate_count > 0:
            print(f"   ⚠️ 存在{duplicate_count}个重复功能，需要整合")
        if redundant_count > 0:
            print(f"   🔄 存在{redundant_count}个重复逻辑，需要重构")
        if compliant_count < 3:
            print(f"   📋 {3-compliant_count}个交易所存在API符合性问题")
        
        if duplicate_count == 0 and redundant_count == 0 and compliant_count == 3:
            print(f"   ✅ 未发现重大功能重叠问题")
        
        print("="*80)

def main():
    """主函数"""
    diagnosis = HealthMonitoringOverlapDiagnosis()
    results = diagnosis.run_diagnosis()
    diagnosis.print_summary()
    
    return results

if __name__ == "__main__":
    main()
