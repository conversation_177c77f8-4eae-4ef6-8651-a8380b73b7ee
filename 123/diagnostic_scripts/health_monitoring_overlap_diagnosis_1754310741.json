{"timestamp": **********, "diagnosis_type": "健康监控和连接池管理功能重叠分析", "overlap_analysis": {"file_distribution": {"system_monitor.py": {"path": "core/system_monitor.py", "functions": [{"function": "def perform_health_check", "pattern": "def.*health.*check", "type": "general_health"}, {"function": "def perform_health_check", "pattern": "def.*health.*check", "type": "general_health"}, {"function": "def _check_arbitrage_engine_health", "pattern": "def.*check.*health", "type": "general_health"}, {"function": "def _check_execution_engine_health", "pattern": "def.*check.*health", "type": "general_health"}, {"function": "def _check_opportunity_scanner_health", "pattern": "def.*check.*health", "type": "general_health"}, {"function": "async def perform_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _attempt_auto_fixes(self, issues: List[Health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _apply_auto_fix(self, issue: Health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _fix_arbitrage_engine_issue(self, issue: Health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _fix_execution_engine_issue(self, issue: Health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def perform_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _check_arbitrage_engine_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _check_execution_engine_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _check_opportunity_scanner_health", "pattern": "async def.*health", "type": "general_health"}], "total_lines": 799, "health_related_lines": 162, "health_percentage": 20.275344180225282}, "ws_client.py": {"path": "websocket/ws_client.py", "functions": [{"function": "def _post_reconnection_health_check", "pattern": "def.*health.*check", "type": "connection_health"}, {"function": "def _handle_health_check", "pattern": "def.*health.*check", "type": "general_health"}, {"function": "async def _post_reconnection_health", "pattern": "async def.*health", "type": "connection_health"}, {"function": "async def _active_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _enhanced_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _handle_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "def _pre_connection_check", "pattern": "def.*connection.*check", "type": "connection_health"}, {"function": "def _post_reconnection_health_check", "pattern": "def.*connection.*check", "type": "connection_health"}, {"function": "def _basic_connection_check", "pattern": "def.*connection.*check", "type": "connection_health"}, {"function": "def _safe_connection_check", "pattern": "def.*connection.*check", "type": "connection_health"}, {"function": "async def _pre_connection_check", "pattern": "async def.*connection.*check", "type": "connection_health"}, {"function": "async def _post_reconnection_health_check", "pattern": "async def.*connection.*check", "type": "connection_health"}, {"function": "async def _basic_connection_check", "pattern": "async def.*connection.*check", "type": "connection_health"}, {"function": "async def _safe_connection_check", "pattern": "async def.*connection.*check", "type": "connection_health"}], "total_lines": 1187, "health_related_lines": 87, "health_percentage": 7.329401853411962}, "ws_manager.py": {"path": "websocket/ws_manager.py", "functions": [{"function": "def _monitor_", "pattern": "def.*_monitor_", "type": "general_health"}, {"function": "async def _monitor_", "pattern": "async def.*_monitor_", "type": "general_health"}], "total_lines": 1283, "health_related_lines": 83, "health_percentage": 6.46921278254092}, "unified_connection_pool_manager.py": {"path": "websocket/unified_connection_pool_manager.py", "functions": [{"function": "def _handle_health_check", "pattern": "def.*health.*check", "type": "general_health"}, {"function": "def start_data_flow_health_check", "pattern": "def.*health.*check", "type": "general_health"}, {"function": "def _check_data_flow_health", "pattern": "def.*check.*health", "type": "general_health"}, {"function": "async def _handle_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def start_data_flow_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "async def _check_data_flow_health", "pattern": "async def.*health", "type": "general_health"}, {"function": "def _connection_monitor_", "pattern": "def.*_monitor_", "type": "connection_health"}, {"function": "def _quality_monitor_", "pattern": "def.*_monitor_", "type": "general_health"}, {"function": "async def _connection_monitor_", "pattern": "async def.*_monitor_", "type": "connection_health"}, {"function": "async def _quality_monitor_", "pattern": "async def.*_monitor_", "type": "general_health"}], "total_lines": 1486, "health_related_lines": 408, "health_percentage": 27.456258411843876}, "performance_monitor.py": {"path": "websocket/performance_monitor.py", "functions": [], "total_lines": 438, "health_related_lines": 50, "health_percentage": 11.415525114155251}}, "pattern_analysis": {"connection_status_check": {"files": [{"file": "ws_client.py", "occurrences": 13, "keywords": ["检查连接状态", "ws.open"]}, {"file": "ws_manager.py", "occurrences": 6, "keywords": ["检查连接状态", "ws.open"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 26, "keywords": ["检查连接状态", "connection.status", "ws.open", "ConnectionStatus.CONNECTED"]}], "total_occurrences": 45}, "health_check_execution": {"files": [{"file": "system_monitor.py", "occurrences": 17, "keywords": ["健康检查", "health_check", "ping"]}, {"file": "ws_client.py", "occurrences": 39, "keywords": ["健康检查", "health_check", "send_heartbeat", "ping"]}, {"file": "ws_manager.py", "occurrences": 8, "keywords": ["健康检查", "health_check", "ping"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 11, "keywords": ["健康检查", "health_check", "ping"]}, {"file": "performance_monitor.py", "occurrences": 1, "keywords": ["ping"]}], "total_occurrences": 76}, "reconnection_logic": {"files": [{"file": "ws_client.py", "occurrences": 119, "keywords": ["重连", "reconnect", "_reconnect", "重新连接"]}, {"file": "ws_manager.py", "occurrences": 32, "keywords": ["重连", "reconnect", "_reconnect"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 129, "keywords": ["重连", "reconnect", "_reconnect", "重新连接"]}, {"file": "performance_monitor.py", "occurrences": 4, "keywords": ["reconnect", "_reconnect"]}], "total_occurrences": 284}, "error_recovery": {"files": [{"file": "system_monitor.py", "occurrences": 25, "keywords": ["修复"]}, {"file": "ws_client.py", "occurrences": 44, "keywords": ["handle_connection_issue", "修复"]}, {"file": "ws_manager.py", "occurrences": 23, "keywords": ["错误恢复", "修复"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 67, "keywords": ["handle_connection_issue", "修复"]}, {"file": "performance_monitor.py", "occurrences": 18, "keywords": ["错误恢复", "error_recovery", "修复"]}], "total_occurrences": 177}}}, "duplicate_functions": [{"pattern": "connection_status_check", "files_count": 3, "total_occurrences": 45, "files": [{"file": "ws_client.py", "occurrences": 13, "keywords": ["检查连接状态", "ws.open"]}, {"file": "ws_manager.py", "occurrences": 6, "keywords": ["检查连接状态", "ws.open"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 26, "keywords": ["检查连接状态", "connection.status", "ws.open", "ConnectionStatus.CONNECTED"]}]}, {"pattern": "health_check_execution", "files_count": 5, "total_occurrences": 76, "files": [{"file": "system_monitor.py", "occurrences": 17, "keywords": ["健康检查", "health_check", "ping"]}, {"file": "ws_client.py", "occurrences": 39, "keywords": ["健康检查", "health_check", "send_heartbeat", "ping"]}, {"file": "ws_manager.py", "occurrences": 8, "keywords": ["健康检查", "health_check", "ping"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 11, "keywords": ["健康检查", "health_check", "ping"]}, {"file": "performance_monitor.py", "occurrences": 1, "keywords": ["ping"]}]}, {"pattern": "reconnection_logic", "files_count": 4, "total_occurrences": 284, "files": [{"file": "ws_client.py", "occurrences": 119, "keywords": ["重连", "reconnect", "_reconnect", "重新连接"]}, {"file": "ws_manager.py", "occurrences": 32, "keywords": ["重连", "reconnect", "_reconnect"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 129, "keywords": ["重连", "reconnect", "_reconnect", "重新连接"]}, {"file": "performance_monitor.py", "occurrences": 4, "keywords": ["reconnect", "_reconnect"]}]}, {"pattern": "error_recovery", "files_count": 5, "total_occurrences": 177, "files": [{"file": "system_monitor.py", "occurrences": 25, "keywords": ["修复"]}, {"file": "ws_client.py", "occurrences": 44, "keywords": ["handle_connection_issue", "修复"]}, {"file": "ws_manager.py", "occurrences": 23, "keywords": ["错误恢复", "修复"]}, {"file": "unified_connection_pool_manager.py", "occurrences": 67, "keywords": ["handle_connection_issue", "修复"]}, {"file": "performance_monitor.py", "occurrences": 18, "keywords": ["错误恢复", "error_recovery", "修复"]}]}], "redundant_logic": [{"name": "WebSocket连接状态检查", "implementations": [{"file": "ws_client.py", "matched_patterns": ["self.ws.open"], "pattern_count": 1}, {"file": "unified_connection_pool_manager.py", "matched_patterns": ["connection.status", "ConnectionStatus.CONNECTED"], "pattern_count": 2}], "redundancy_score": 20}, {"name": "健康检查触发逻辑", "implementations": [{"file": "ws_client.py", "matched_patterns": ["health_check", "silent_duration", "last_message_time"], "pattern_count": 3}, {"file": "system_monitor.py", "matched_patterns": ["health_check"], "pattern_count": 1}, {"file": "unified_connection_pool_manager.py", "matched_patterns": ["health_check"], "pattern_count": 1}], "redundancy_score": 30}, {"name": "重连逻辑", "implementations": [{"file": "ws_client.py", "matched_patterns": ["reconnect", "_reconnect", "重新连接"], "pattern_count": 3}, {"file": "ws_manager.py", "matched_patterns": ["reconnect", "_reconnect"], "pattern_count": 2}, {"file": "unified_connection_pool_manager.py", "matched_patterns": ["reconnect", "_reconnect", "重新连接"], "pattern_count": 3}], "redundancy_score": 30}, {"name": "错误处理和恢复", "implementations": [{"file": "ws_client.py", "matched_patterns": ["handle_connection_issue"], "pattern_count": 1}, {"file": "unified_connection_pool_manager.py", "matched_patterns": ["handle_connection_issue"], "pattern_count": 1}, {"file": "system_monitor.py", "matched_patterns": ["auto_fix"], "pattern_count": 1}], "redundancy_score": 30}], "integration_opportunities": [], "api_compliance_check": {"gate_io": {"compliant": true, "issues": []}, "bybit": {"compliant": false, "issues": ["缺少rate_limiting"]}, "okx": {"compliant": false, "issues": ["缺少reconnection_logic"]}}, "optimization_recommendations": [{"priority": "HIGH", "category": "功能整合", "title": "统一健康监控功能", "description": "发现4个重复功能模式，建议整合到统一连接池管理器", "actions": ["将ws_client.py中的_active_health_monitoring完全委托给连接池管理器", "移除ws_manager.py中的重复健康检查逻辑", "统一system_monitor.py中的WebSocket健康检查接口"]}, {"priority": "MEDIUM", "category": "逻辑优化", "title": "消除重复逻辑", "description": "发现3个高冗余逻辑，需要重构", "actions": ["创建统一的连接状态检查接口", "实施单一职责的健康检查策略", "建立统一的错误恢复机制"]}, {"priority": "HIGH", "category": "API符合性", "title": "修复API规则符合性问题", "description": "2个交易所存在API符合性问题", "actions": ["修复bybit, okx的API符合性问题", "确保所有交易所都实现标准的健康监控接口", "统一错误处理和重连逻辑"]}]}