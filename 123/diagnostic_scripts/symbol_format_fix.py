#!/usr/bin/env python3
"""
交易对格式修复脚本
解决Bybit交易对不存在的问题

执行: python3 diagnostic_scripts/symbol_format_fix.py
"""

import os
import sys
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class SymbolFormatFixer:
    def __init__(self):
        self.project_root = project_root
        self.supported_symbols = []
        self.exchange_symbols = {
            "bybit": {},
            "gate": {},
            "okx": {}
        }
    
    async def check_bybit_symbols(self):
        """检查Bybit支持的交易对"""
        print("🔍 检查Bybit支持的交易对...")
        
        try:
            from exchanges.bybit_exchange import BybitExchange
            from exchanges.currency_adapter import get_exchange_symbol
            
            # 创建Bybit交易所实例
            bybit = BybitExchange()
            
            # 测试交易对列表
            test_symbols = ["ADA-USDT", "DOGE-USDT", "SOL-USDT", "AVAX-USDT", "DOT-USDT", "SHIB-USDT", "BNB-USDT"]
            
            print(f"📊 测试 {len(test_symbols)} 个交易对...")
            
            for symbol in test_symbols:
                # 转换为Bybit格式
                bybit_symbol = get_exchange_symbol(symbol, "bybit", "futures")
                print(f"  {symbol} -> {bybit_symbol}")
                
                # 检查期货合约是否存在
                try:
                    contract_info = await bybit._get_contract_info(symbol)
                    if contract_info:
                        print(f"    ✅ 期货合约存在")
                        self.exchange_symbols["bybit"][symbol] = {
                            "symbol": bybit_symbol,
                            "futures_available": True,
                            "contract_info": contract_info
                        }
                    else:
                        print(f"    ❌ 期货合约不存在")
                        self.exchange_symbols["bybit"][symbol] = {
                            "symbol": bybit_symbol,
                            "futures_available": False,
                            "error": "合约不存在"
                        }
                except Exception as e:
                    print(f"    ❌ 检查失败: {e}")
                    self.exchange_symbols["bybit"][symbol] = {
                        "symbol": bybit_symbol,
                        "futures_available": False,
                        "error": str(e)
                    }
                
                # 添加延迟避免API限流
                await asyncio.sleep(0.2)
                
        except Exception as e:
            print(f"❌ Bybit检查失败: {e}")
    
    def analyze_symbol_issues(self):
        """分析交易对问题"""
        print("\n🔍 分析交易对问题...")
        
        issues = []
        recommendations = []
        
        # 检查Bybit不支持的交易对
        bybit_unsupported = []
        for symbol, info in self.exchange_symbols["bybit"].items():
            if not info.get("futures_available", False):
                bybit_unsupported.append(symbol)
                issues.append({
                    "type": "BYBIT_UNSUPPORTED_SYMBOL",
                    "symbol": symbol,
                    "exchange": "bybit",
                    "description": f"Bybit不支持{symbol}期货交易",
                    "error": info.get("error", "未知错误")
                })
        
        if bybit_unsupported:
            print(f"❌ Bybit不支持的交易对: {', '.join(bybit_unsupported)}")
            recommendations.append({
                "action": "从TARGET_SYMBOLS中移除Bybit不支持的交易对",
                "symbols_to_remove": bybit_unsupported,
                "reason": "避免Bybit期货数据键为0的问题"
            })
        else:
            print("✅ 所有交易对都被Bybit支持")
        
        return issues, recommendations
    
    def generate_fixed_env_config(self, recommendations):
        """生成修复后的.env配置"""
        print("\n🔧 生成修复后的.env配置...")
        
        # 读取当前.env文件
        env_file = self.project_root / ".env"
        if not env_file.exists():
            print("❌ .env文件不存在")
            return
        
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        # 查找TARGET_SYMBOLS行
        lines = env_content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('TARGET_SYMBOLS='):
                current_symbols = line.split('=')[1].split(',')
                current_symbols = [s.strip() for s in current_symbols if s.strip()]
                
                print(f"当前交易对: {current_symbols}")
                
                # 应用修复建议
                for rec in recommendations:
                    if rec["action"].startswith("从TARGET_SYMBOLS中移除"):
                        symbols_to_remove = rec["symbols_to_remove"]
                        current_symbols = [s for s in current_symbols if s not in symbols_to_remove]
                        print(f"移除不支持的交易对: {symbols_to_remove}")
                
                # 生成新的TARGET_SYMBOLS行
                new_symbols_line = f"TARGET_SYMBOLS={','.join(current_symbols)}"
                lines[i] = new_symbols_line
                
                print(f"修复后交易对: {current_symbols}")
                break
        
        # 保存修复后的配置
        fixed_env_content = '\n'.join(lines)
        backup_file = self.project_root / ".env.backup"
        
        # 备份原文件
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print(f"✅ 原.env文件已备份到: {backup_file}")
        
        # 写入修复后的文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(fixed_env_content)
        print(f"✅ 修复后的.env文件已保存")
        
        return current_symbols
    
    def save_analysis_report(self, issues, recommendations):
        """保存分析报告"""
        report = {
            "timestamp": int(time.time()),
            "analysis_time": datetime.now().isoformat(),
            "exchange_symbols": self.exchange_symbols,
            "issues": issues,
            "recommendations": recommendations
        }
        
        report_file = self.project_root / "diagnostic_scripts" / f"symbol_format_analysis_{report['timestamp']}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 分析报告已保存: {report_file}")
        return report_file
    
    def print_summary(self, issues, recommendations):
        """打印修复摘要"""
        print("\n" + "="*80)
        print("🏛️ 交易对格式修复报告")
        print("="*80)
        
        print(f"\n❌ 发现问题 ({len(issues)}个):")
        for issue in issues:
            print(f"  • {issue['symbol']}: {issue['description']}")
        
        print(f"\n🔧 修复建议 ({len(recommendations)}个):")
        for rec in recommendations:
            print(f"  • {rec['action']}")
            if "symbols_to_remove" in rec:
                print(f"    移除: {', '.join(rec['symbols_to_remove'])}")
        
        print("="*80)
    
    async def run_fix(self):
        """运行完整修复"""
        print("🚀 启动交易对格式修复...")
        print(f"📁 项目根目录: {self.project_root}")
        
        # 检查Bybit交易对支持情况
        await self.check_bybit_symbols()
        
        # 分析问题
        issues, recommendations = self.analyze_symbol_issues()
        
        # 打印摘要
        self.print_summary(issues, recommendations)
        
        # 如果有修复建议，应用修复
        if recommendations:
            print("\n🔧 应用修复建议...")
            fixed_symbols = self.generate_fixed_env_config(recommendations)
            
            # 保存分析报告
            import time
            from datetime import datetime
            self.save_analysis_report(issues, recommendations)
            
            print(f"\n✅ 修复完成！新的交易对列表: {fixed_symbols}")
            print("⚠️ 请重启系统以应用新的交易对配置")
        else:
            print("\n✅ 没有发现需要修复的问题")
        
        return issues, recommendations

async def main():
    """主函数"""
    fixer = SymbolFormatFixer()
    issues, recommendations = await fixer.run_fix()
    
    # 返回退出码
    if len(issues) > 0:
        sys.exit(1)  # 有问题需要修复
    else:
        sys.exit(0)  # 正常

if __name__ == "__main__":
    asyncio.run(main())
