{"timestamp": "2025-08-03T14:32:23.807333", "diagnosis_summary": {"total_issues": 0, "critical_issues": 0, "high_priority_issues": 0, "medium_priority_issues": 0, "overall_health": "GOOD", "unified_modules_found": 6, "consistency_issues": 1, "recommendations_count": 0}, "critical_issues": [], "high_priority_issues": [], "medium_priority_issues": [], "architecture_analysis": {"unified_modules_count": 6, "websocket_modules": ["unified_timestamp_processor", "unified_connection_pool_manager", "unified_data_formatter"], "core_modules": ["core/unified_order_spread_calculator", "core/unified_balance_manager", "core/unified_depth_analyzer"], "missing_modules": [], "duplicate_implementations": [], "interface_analysis": {"websocket_chain": {"has_manager": true, "manages_connections": true, "handles_errors": false, "monitors_performance": true}, "timestamp_chain": {"has_processor": true, "extracts_server_timestamp": true, "validates_freshness": true, "syncs_across_exchanges": false}, "api_chain": {}, "broken_chains": []}, "consistency_analysis": {"websocket_implementations": {"gate": {"websocket_exists": true, "exchange_exists": true, "timestamp_method": "instance", "has_monitoring": false, "heartbeat_interval": 20}, "bybit": {"websocket_exists": true, "exchange_exists": true, "timestamp_method": "instance", "has_monitoring": false, "heartbeat_interval": "unknown"}, "okx": {"websocket_exists": true, "exchange_exists": true, "timestamp_method": "instance", "has_monitoring": true, "heartbeat_interval": "unknown"}}, "timestamp_processing": {}, "api_handling": {}, "inconsistencies": [{"type": "监控机制不一致", "details": "部分交易所有监控机制，部分没有: {False, True}", "severity": "HIGH"}]}, "duplication_analysis": {"duplicate_functions": [{"function": "calculate_data_age", "file": "./diagnostic_scripts/comprehensive_system_diagnosis.py", "type": "数据年龄计算重复实现"}, {"function": "calculate_data_age", "file": "./websocket/unified_timestamp_processor.py", "type": "数据年龄计算重复实现"}, {"function": "calculate_data_age", "file": "./core/data_snapshot_validator.py", "type": "数据年龄计算重复实现"}, {"function": "get_synced_timestamp", "files": ["./diagnostic_scripts/comprehensive_system_diagnosis.py", "./websocket/unified_timestamp_processor.py", "./core/data_snapshot_validator.py"], "type": "时间戳同步重复实现"}], "redundant_imports": [], "similar_implementations": [], "optimization_opportunities": []}}, "unified_modules_status": {"websocket_implementations": {"gate_ws.py": {"uses_unified_timestamp": true, "uses_connection_pool": false, "has_monitor_data_flow": false, "has_handle_blocking": false, "import_style": "global"}, "bybit_ws.py": {"uses_unified_timestamp": true, "uses_connection_pool": false, "has_monitor_data_flow": false, "has_handle_blocking": false, "import_style": "global"}, "okx_ws.py": {"uses_unified_timestamp": true, "uses_connection_pool": false, "has_monitor_data_flow": true, "has_handle_blocking": true, "import_style": "global"}}, "import_patterns": {}, "consistency_issues": [{"file": "okx_ws.py", "issue": "WebSocket并发冲突风险", "description": "存在_monitor_data_flow方法可能导致并发冲突"}]}, "websocket_health": {"okx_issues": [{"issue": "WebSocket并发冲突", "description": "存在_monitor_data_flow()监控任务与主消息循环冲突", "severity": "CRITICAL", "line_evidence": true}, {"issue": "数据流阻塞处理", "description": "存在_handle_data_flow_blocking()方法", "severity": "HIGH", "line_evidence": true}], "gate_issues": [], "bybit_status": [{"has_monitoring": false, "uses_unified_timestamp": true, "heartbeat_compliant": false, "status": "COMPLIANT"}], "blocking_patterns": []}, "api_optimization_status": {"rate_limit_settings": {"gate": 8, "bybit": 4, "okx": 3, "base_cooldown": 1, "buffer_cooldown": 3, "total_cooldown": 5, "expected_interval": 1}, "optimization_issues": [], "recommendations": []}, "recommendations": []}