#!/usr/bin/env python3
"""
统一逻辑验证脚本
验证重复逻辑是否已完全消除，确保100%统一实现
"""

import os
import sys
import json
import time
import re
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class UnifiedLogicVerification:
    """统一逻辑验证器"""
    
    def __init__(self):
        self.project_root = project_root
        self.verification_results = {
            "timestamp": int(time.time()),
            "verification_type": "统一逻辑验证",
            "duplicate_elimination": {},
            "unified_implementation": {},
            "remaining_issues": [],
            "compliance_score": 0,
            "is_fully_unified": False
        }
    
    def run_verification(self):
        """运行统一逻辑验证"""
        print("🔍 开始统一逻辑验证...")
        print("=" * 80)
        
        # 1. 验证重复逻辑消除
        self._verify_duplicate_elimination()
        
        # 2. 验证统一实现
        self._verify_unified_implementation()
        
        # 3. 计算合规分数
        self._calculate_compliance_score()
        
        # 4. 保存验证结果
        self._save_verification_results()
        
        return self.verification_results
    
    def _verify_duplicate_elimination(self):
        """验证重复逻辑消除"""
        print("📊 验证重复逻辑消除...")
        
        elimination_check = {
            "health_monitoring_removed": False,
            "connection_check_unified": False,
            "reconnection_logic_unified": False,
            "error_recovery_unified": False
        }
        
        # 检查ws_client.py中的健康监控是否已移除重复逻辑
        ws_client_file = self.project_root / "websocket" / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含统一实现标记
            if "100%委托给统一连接池管理器" in content and "完全移除重复逻辑" in content:
                elimination_check["health_monitoring_removed"] = True
        
        # 检查ws_manager.py中的监控逻辑是否已统一
        ws_manager_file = self.project_root / "websocket" / "ws_manager.py"
        if ws_manager_file.exists():
            with open(ws_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "100%统一到连接池管理器" in content and "本地监控逻辑已移除" in content:
                elimination_check["connection_check_unified"] = True
        
        # 检查system_monitor.py中的WebSocket检查是否已统一
        system_monitor_file = self.project_root / "core" / "system_monitor.py"
        if system_monitor_file.exists():
            with open(system_monitor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "100%委托给统一连接池管理器" in content and "移除重复逻辑" in content:
                elimination_check["reconnection_logic_unified"] = True
        
        # 检查统一连接池管理器是否有统一接口
        pool_manager_file = self.project_root / "websocket" / "unified_connection_pool_manager.py"
        if pool_manager_file.exists():
            with open(pool_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "get_overall_health_status" in content and "统一健康状态接口" in content:
                elimination_check["error_recovery_unified"] = True
        
        self.verification_results["duplicate_elimination"] = elimination_check
        
        eliminated_count = sum(elimination_check.values())
        print(f"   ✅ 重复逻辑消除: {eliminated_count}/4 完成")
    
    def _verify_unified_implementation(self):
        """验证统一实现"""
        print("🔧 验证统一实现...")
        
        unified_check = {
            "single_health_monitoring_entry": False,
            "unified_connection_pool_usage": False,
            "consistent_error_handling": False,
            "no_duplicate_functions": False
        }
        
        # 检查是否只有统一连接池管理器执行健康监控
        health_monitoring_files = []
        for file_name in ["ws_client.py", "ws_manager.py", "system_monitor.py"]:
            if file_name == "system_monitor.py":
                file_path = self.project_root / "core" / file_name
            else:
                file_path = self.project_root / "websocket" / file_name
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否还有本地健康监控逻辑
                if "while self.running" in content and "health_check" in content.lower():
                    # 排除已统一的实现
                    if "委托给统一连接池管理器" not in content:
                        health_monitoring_files.append(file_name)
        
        if len(health_monitoring_files) == 0:
            unified_check["single_health_monitoring_entry"] = True
        
        # 检查统一连接池管理器使用
        pool_usage_count = 0
        for file_name in ["ws_client.py", "ws_manager.py", "system_monitor.py"]:
            if file_name == "system_monitor.py":
                file_path = self.project_root / "core" / file_name
            else:
                file_path = self.project_root / "websocket" / file_name
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "unified_connection_pool_manager" in content or "get_connection_pool_manager" in content:
                    pool_usage_count += 1
        
        if pool_usage_count >= 3:
            unified_check["unified_connection_pool_usage"] = True
        
        # 检查错误处理一致性
        error_handling_patterns = []
        for exchange in ["gate", "bybit", "okx"]:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查错误处理模式
                has_debug_logging = "_log_debug" in content
                has_smart_filtering = "智能过滤" in content or "自动过滤" in content
                
                error_handling_patterns.append({
                    "exchange": exchange,
                    "debug_logging": has_debug_logging,
                    "smart_filtering": has_smart_filtering
                })
        
        # 检查是否一致
        if len(error_handling_patterns) > 0:
            first_pattern = error_handling_patterns[0]
            all_consistent = all(
                p["debug_logging"] == first_pattern["debug_logging"] and
                p["smart_filtering"] == first_pattern["smart_filtering"]
                for p in error_handling_patterns
            )
            unified_check["consistent_error_handling"] = all_consistent
        
        # 检查是否没有重复函数
        duplicate_functions = self._count_duplicate_functions()
        unified_check["no_duplicate_functions"] = duplicate_functions == 0
        
        self.verification_results["unified_implementation"] = unified_check
        
        unified_count = sum(unified_check.values())
        print(f"   ✅ 统一实现: {unified_count}/4 完成")
    
    def _count_duplicate_functions(self) -> int:
        """统计重复函数数量"""
        function_implementations = {}
        
        # 检查健康监控相关函数
        health_functions = ["_active_health_monitoring", "_monitor_status", "_check_websocket_connections"]
        
        for func_name in health_functions:
            implementations = []
            
            for file_name in ["ws_client.py", "ws_manager.py", "system_monitor.py"]:
                if file_name == "system_monitor.py":
                    file_path = self.project_root / "core" / file_name
                else:
                    file_path = self.project_root / "websocket" / file_name
                
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if f"def {func_name}" in content or f"async def {func_name}" in content:
                        # 检查是否是统一实现（委托给连接池管理器）
                        if "委托给统一连接池管理器" in content or "100%统一" in content:
                            continue  # 这是统一实现，不算重复
                        implementations.append(file_name)
            
            if len(implementations) > 1:
                function_implementations[func_name] = implementations
        
        return len(function_implementations)
    
    def _calculate_compliance_score(self):
        """计算合规分数"""
        elimination = self.verification_results["duplicate_elimination"]
        unified = self.verification_results["unified_implementation"]
        
        elimination_score = sum(elimination.values()) / len(elimination) * 50
        unified_score = sum(unified.values()) / len(unified) * 50
        
        total_score = elimination_score + unified_score
        
        self.verification_results["compliance_score"] = total_score
        self.verification_results["is_fully_unified"] = total_score >= 95
        
        print(f"📊 合规分数: {total_score:.1f}/100")
        
        if total_score >= 95:
            print("🎉 完全统一实现！")
        elif total_score >= 80:
            print("✅ 基本统一实现")
        else:
            print("⚠️ 仍存在重复逻辑")
    
    def _save_verification_results(self):
        """保存验证结果"""
        timestamp = int(time.time())
        filename = f"unified_logic_verification_{timestamp}.json"
        filepath = self.project_root / "diagnostic_scripts" / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 验证结果已保存到: diagnostic_scripts/{filename}")
    
    def print_summary(self):
        """打印验证摘要"""
        print("\n" + "="*80)
        print("🔍 统一逻辑验证摘要")
        print("="*80)
        
        elimination = self.verification_results["duplicate_elimination"]
        unified = self.verification_results["unified_implementation"]
        
        print("📊 重复逻辑消除状态:")
        for key, value in elimination.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {'已完成' if value else '未完成'}")
        
        print("\n🔧 统一实现状态:")
        for key, value in unified.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {'已完成' if value else '未完成'}")
        
        score = self.verification_results["compliance_score"]
        is_unified = self.verification_results["is_fully_unified"]
        
        print(f"\n🏆 总体评估:")
        print(f"   合规分数: {score:.1f}/100")
        print(f"   完全统一: {'是' if is_unified else '否'}")
        
        if is_unified:
            print("🎉 恭喜！重复逻辑已完全消除，实现100%统一！")
        else:
            print("⚠️ 仍需进一步统一实现")
        
        print("="*80)

def main():
    """主函数"""
    verification = UnifiedLogicVerification()
    results = verification.run_verification()
    verification.print_summary()
    
    return results

if __name__ == "__main__":
    main()
