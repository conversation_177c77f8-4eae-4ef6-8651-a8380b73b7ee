#!/usr/bin/env python3
"""
WebSocket架构设计问题精确诊断脚本
专门诊断多协程并发访问同一WebSocket连接的问题
"""

import os
import sys
import json
import re
import time
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WebSocketArchitectureDiagnosis:
    """WebSocket架构设计问题诊断器"""
    
    def __init__(self):
        self.project_root = project_root
        self.diagnosis_results = {
            "timestamp": int(time.time()),
            "diagnosis_type": "WebSocket架构设计问题",
            "critical_issues": [],
            "high_issues": [],
            "medium_issues": [],
            "architecture_analysis": {},
            "websocket_single_consumer_violations": [],
            "concurrent_access_patterns": [],
            "recommendations": []
        }
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始WebSocket架构设计问题诊断...")
        
        # 1. 分析WebSocket单一消费者原则违反
        self._analyze_single_consumer_violations()
        
        # 2. 检测并发访问模式
        self._detect_concurrent_access_patterns()
        
        # 3. 分析架构设计缺陷
        self._analyze_architecture_defects()
        
        # 4. 检查SHIB交易对过滤机制
        self._check_shib_filtering_mechanism()
        
        # 5. 生成修复建议
        self._generate_recommendations()
        
        # 6. 保存诊断结果
        self._save_diagnosis_results()
        
        return self.diagnosis_results
    
    def _analyze_single_consumer_violations(self):
        """分析WebSocket单一消费者原则违反"""
        print("📊 分析WebSocket单一消费者原则违反...")
        
        violations = []
        
        # 检查ws_client.py基类
        ws_client_file = self.project_root / "websocket" / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查主消息循环run()方法
            if "await self.ws.recv()" in content:
                violations.append({
                    "file": "websocket/ws_client.py",
                    "method": "run()",
                    "line_pattern": "await self.ws.recv()",
                    "description": "主消息循环调用ws.recv()",
                    "severity": "INFO",
                    "note": "这是正确的，主循环应该是唯一调用recv()的地方"
                })
            
            # 检查健康监控任务
            if "_active_health_monitoring" in content:
                violations.append({
                    "file": "websocket/ws_client.py", 
                    "method": "_active_health_monitoring()",
                    "description": "健康监控任务可能与主循环并发",
                    "severity": "HIGH",
                    "impact": "可能导致多个协程同时访问WebSocket连接"
                })
            
            # 检查心跳任务
            if "_heartbeat_loop" in content:
                violations.append({
                    "file": "websocket/ws_client.py",
                    "method": "_heartbeat_loop()",
                    "description": "心跳任务可能与主循环并发",
                    "severity": "MEDIUM",
                    "impact": "心跳发送可能与消息接收冲突"
                })
        
        # 检查Gate.io WebSocket实现
        gate_ws_file = self.project_root / "websocket" / "gate_ws.py"
        if gate_ws_file.exists():
            with open(gate_ws_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有额外的监控任务
            if "_monitor_data_flow" in content:
                violations.append({
                    "file": "websocket/gate_ws.py",
                    "method": "_monitor_data_flow()",
                    "description": "Gate.io存在数据流监控任务",
                    "severity": "CRITICAL",
                    "impact": "与主消息循环产生WebSocket并发冲突"
                })
        
        # 检查OKX WebSocket实现
        okx_ws_file = self.project_root / "websocket" / "okx_ws.py"
        if okx_ws_file.exists():
            with open(okx_ws_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有额外的监控任务
            if "_monitor_data_flow" in content:
                violations.append({
                    "file": "websocket/okx_ws.py",
                    "method": "_monitor_data_flow()",
                    "description": "OKX存在数据流监控任务",
                    "severity": "CRITICAL",
                    "impact": "与主消息循环产生WebSocket并发冲突"
                })
            
            # 检查并发订阅
            if "asyncio.gather" in content and "subscription_tasks" in content:
                violations.append({
                    "file": "websocket/okx_ws.py",
                    "method": "subscribe_channels()",
                    "description": "OKX使用asyncio.gather并发订阅",
                    "severity": "HIGH",
                    "impact": "订阅阶段可能与主消息循环冲突"
                })
        
        self.diagnosis_results["websocket_single_consumer_violations"] = violations
    
    def _detect_concurrent_access_patterns(self):
        """检测并发访问模式"""
        print("🔍 检测WebSocket并发访问模式...")
        
        patterns = []
        
        # 检查连接池管理器
        pool_manager_file = self.project_root / "websocket" / "unified_connection_pool_manager.py"
        if pool_manager_file.exists():
            with open(pool_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "handle_connection_issue" in content:
                patterns.append({
                    "pattern": "连接池管理器干预",
                    "file": "websocket/unified_connection_pool_manager.py",
                    "method": "handle_connection_issue()",
                    "description": "连接池管理器可能触发额外的WebSocket操作",
                    "risk_level": "MEDIUM",
                    "mitigation": "确保连接池操作不直接调用WebSocket recv()"
                })
        
        # 检查WebSocket管理器
        ws_manager_file = self.project_root / "websocket" / "ws_manager.py"
        if ws_manager_file.exists():
            with open(ws_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "restart_clients" in content:
                patterns.append({
                    "pattern": "WebSocket管理器重启",
                    "file": "websocket/ws_manager.py",
                    "description": "WebSocket管理器可能在运行时重启客户端",
                    "risk_level": "LOW",
                    "note": "重启操作通常是安全的"
                })
        
        self.diagnosis_results["concurrent_access_patterns"] = patterns
    
    def _analyze_architecture_defects(self):
        """分析架构设计缺陷"""
        print("🏗️ 分析WebSocket架构设计缺陷...")
        
        defects = {
            "coupling_issues": [],
            "message_queue_missing": False,
            "redundant_logic": []
        }
        
        # 检查过度耦合问题
        ws_client_file = self.project_root / "websocket" / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查连接管理与消息处理是否混合
            if "handle_message" in content and "_connect" in content:
                defects["coupling_issues"].append({
                    "issue": "连接管理与消息处理混合",
                    "file": "websocket/ws_client.py",
                    "description": "连接管理和消息处理在同一个类中",
                    "impact": "增加了架构复杂性"
                })
            
            # 检查是否有统一消息分发机制
            if "_message_queue" in content or "_unified_message_distributor" in content:
                defects["message_queue_missing"] = False
            else:
                defects["message_queue_missing"] = True
        
        # 检查重复逻辑
        health_monitoring_files = []
        for ws_file in ["gate_ws.py", "bybit_ws.py", "okx_ws.py"]:
            file_path = self.project_root / "websocket" / ws_file
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                if "_health_monitoring" in content or "_monitor_data_flow" in content:
                    health_monitoring_files.append(ws_file)
        
        if len(health_monitoring_files) > 1:
            defects["redundant_logic"].append({
                "issue": "重复健康监控逻辑",
                "files": health_monitoring_files,
                "description": "多个WebSocket客户端实现了相似的健康监控逻辑"
            })
        
        self.diagnosis_results["architecture_analysis"] = defects
    
    def _check_shib_filtering_mechanism(self):
        """检查SHIB交易对过滤机制"""
        print("🔧 检查SHIB交易对过滤机制...")
        
        shib_analysis = {
            "validator_exists": False,
            "bybit_shib_filtered": False,
            "error_handling_optimized": False,
            "graceful_degradation": False
        }
        
        # 检查统一交易对验证器
        validator_file = self.project_root / "core" / "unified_symbol_validator.py"
        if validator_file.exists():
            shib_analysis["validator_exists"] = True
            
            with open(validator_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查SHIB过滤逻辑
            if "SHIB" in content and "bybit" in content and "futures" in content:
                shib_analysis["bybit_shib_filtered"] = True
        
        # 检查WebSocket错误处理优化
        for exchange in ["gate", "bybit", "okx"]:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查错误级别优化
                if "symbol invalid" in content.lower() or "unknown currency pair" in content.lower():
                    if "_log_debug" in content or "DEBUG" in content:
                        shib_analysis["error_handling_optimized"] = True
                
                # 检查优雅降级
                if "自动过滤" in content or "智能过滤" in content:
                    shib_analysis["graceful_degradation"] = True
        
        self.diagnosis_results["shib_filtering_analysis"] = shib_analysis
    
    def _generate_recommendations(self):
        """生成修复建议"""
        print("💡 生成修复建议...")
        
        recommendations = []
        
        # 基于诊断结果生成建议
        violations = self.diagnosis_results["websocket_single_consumer_violations"]
        critical_violations = [v for v in violations if v.get("severity") == "CRITICAL"]
        
        if critical_violations:
            recommendations.append({
                "priority": "CRITICAL",
                "title": "遵守WebSocket单一消费者原则",
                "description": "移除所有与主消息循环并发的WebSocket操作",
                "actions": [
                    "移除_monitor_data_flow()监控任务",
                    "使用消息队列机制替代直接WebSocket操作",
                    "确保只有主循环调用ws.recv()"
                ]
            })
        
        # SHIB过滤建议
        shib_analysis = self.diagnosis_results.get("shib_filtering_analysis", {})
        if not shib_analysis.get("bybit_shib_filtered"):
            recommendations.append({
                "priority": "HIGH",
                "title": "实施SHIB交易对过滤机制",
                "description": "在系统启动前添加交易对验证，自动过滤不支持的交易对",
                "actions": [
                    "实施交易对预验证机制",
                    "添加交易所特定的交易对映射表",
                    "将'交易对不存在'从ERROR级别降为DEBUG级别",
                    "实施优雅降级机制"
                ]
            })
        
        # 架构优化建议
        defects = self.diagnosis_results.get("architecture_analysis", {})
        if defects.get("message_queue_missing"):
            recommendations.append({
                "priority": "MEDIUM",
                "title": "实施统一消息分发机制",
                "description": "添加消息队列避免直接WebSocket并发访问",
                "actions": [
                    "实施消息队列机制",
                    "分离连接管理与消息处理",
                    "统一错误处理逻辑"
                ]
            })
        
        self.diagnosis_results["recommendations"] = recommendations
    
    def _save_diagnosis_results(self):
        """保存诊断结果"""
        timestamp = int(time.time())
        filename = f"websocket_architecture_diagnosis_{timestamp}.json"
        filepath = self.project_root / "diagnostic_scripts" / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 诊断结果已保存到: {filename}")
    
    def print_summary(self):
        """打印诊断摘要"""
        print("\n" + "="*60)
        print("🔍 WebSocket架构设计问题诊断摘要")
        print("="*60)
        
        violations = self.diagnosis_results["websocket_single_consumer_violations"]
        critical_count = len([v for v in violations if v.get("severity") == "CRITICAL"])
        high_count = len([v for v in violations if v.get("severity") == "HIGH"])
        
        print(f"🚨 严重问题: {critical_count}个")
        print(f"⚠️ 高级问题: {high_count}个")
        
        if critical_count > 0:
            print("\n🚨 严重问题详情:")
            for v in violations:
                if v.get("severity") == "CRITICAL":
                    print(f"  - {v['file']}: {v['description']}")
        
        recommendations = self.diagnosis_results["recommendations"]
        print(f"\n💡 修复建议: {len(recommendations)}条")
        for rec in recommendations:
            print(f"  - [{rec['priority']}] {rec['title']}")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    diagnosis = WebSocketArchitectureDiagnosis()
    results = diagnosis.run_diagnosis()
    diagnosis.print_summary()
    
    return results

if __name__ == "__main__":
    main()
