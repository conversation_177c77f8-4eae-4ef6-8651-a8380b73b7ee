{"timestamp": **********, "diagnosis_type": "WebSocket架构设计问题", "critical_issues": [], "high_issues": [], "medium_issues": [], "architecture_analysis": {"coupling_issues": [{"issue": "连接管理与消息处理混合", "file": "websocket/ws_client.py", "description": "连接管理和消息处理在同一个类中", "impact": "增加了架构复杂性"}], "message_queue_missing": false, "redundant_logic": []}, "websocket_single_consumer_violations": [{"file": "websocket/ws_client.py", "method": "_active_health_monitoring()", "description": "健康监控任务可能与主循环并发", "severity": "HIGH", "impact": "可能导致多个协程同时访问WebSocket连接"}, {"file": "websocket/ws_client.py", "method": "_heartbeat_loop()", "description": "心跳任务可能与主循环并发", "severity": "MEDIUM", "impact": "心跳发送可能与消息接收冲突"}, {"file": "websocket/okx_ws.py", "method": "_monitor_data_flow()", "description": "OKX存在数据流监控任务", "severity": "CRITICAL", "impact": "与主消息循环产生WebSocket并发冲突"}], "concurrent_access_patterns": [{"pattern": "连接池管理器干预", "file": "websocket/unified_connection_pool_manager.py", "method": "handle_connection_issue()", "description": "连接池管理器可能触发额外的WebSocket操作", "risk_level": "MEDIUM", "mitigation": "确保连接池操作不直接调用WebSocket recv()"}, {"pattern": "WebSocket管理器重启", "file": "websocket/ws_manager.py", "description": "WebSocket管理器可能在运行时重启客户端", "risk_level": "LOW", "note": "重启操作通常是安全的"}], "recommendations": [{"priority": "CRITICAL", "title": "遵守WebSocket单一消费者原则", "description": "移除所有与主消息循环并发的WebSocket操作", "actions": ["移除_monitor_data_flow()监控任务", "使用消息队列机制替代直接WebSocket操作", "确保只有主循环调用ws.recv()"]}], "shib_filtering_analysis": {"validator_exists": true, "bybit_shib_filtered": true, "error_handling_optimized": true, "graceful_degradation": true}}