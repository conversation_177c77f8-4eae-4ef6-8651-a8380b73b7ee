#!/usr/bin/env python3
"""
WebSocket数据流阻塞问题精确诊断脚本
基于日志分析的深度诊断和修复验证

执行: python3 diagnostic_scripts/websocket_blocking_diagnosis.py
"""

import os
import sys
import json
import time
import re
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WebSocketBlockingDiagnosis:
    def __init__(self):
        self.project_root = project_root
        self.logs_dir = self.project_root / "logs"
        self.diagnosis_results = {
            "timestamp": int(time.time()),
            "diagnosis_time": datetime.now().isoformat(),
            "critical_issues": [],
            "high_issues": [],
            "medium_issues": [],
            "recommendations": []
        }
    
    def analyze_error_logs(self):
        """分析错误日志文件"""
        error_log = self.logs_dir / "error_20250804.log"
        if not error_log.exists():
            return
        
        print("🔍 分析错误日志...")
        
        with open(error_log, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. OKX WebSocket并发冲突检测
        concurrent_errors = re.findall(r'cannot call recv while another coroutine is already waiting', content)
        if concurrent_errors:
            self.diagnosis_results["critical_issues"].append({
                "type": "OKX_WEBSOCKET_CONCURRENT_CONFLICT",
                "count": len(concurrent_errors),
                "description": "OKX WebSocket多协程并发调用recv()导致冲突",
                "impact": "数据流完全阻塞",
                "fix_required": "移除并发监控任务，使用单线程接收"
            })
        
        # 2. API限流错误检测
        api_limit_errors = re.findall(r'Too Many Requests.*50011', content)
        if api_limit_errors:
            self.diagnosis_results["medium_issues"].append({
                "type": "OKX_API_RATE_LIMIT",
                "count": len(api_limit_errors),
                "description": "OKX API调用频率超限",
                "impact": "API调用失败，影响系统初始化",
                "fix_required": "优化API调用频率，增加退避机制"
            })
        
        # 3. Bybit交易对错误检测
        bybit_symbol_errors = re.findall(r'symbol invalid.*SHIB', content)
        if bybit_symbol_errors:
            self.diagnosis_results["medium_issues"].append({
                "type": "BYBIT_SYMBOL_FORMAT_ERROR", 
                "count": len(bybit_symbol_errors),
                "description": "Bybit交易对格式错误(SHIB-USDT vs SHIBUSDT)",
                "impact": "Bybit期货数据键为0，套利检测失败",
                "fix_required": "统一交易对格式转换"
            })
        
        print(f"✅ 错误日志分析完成，发现 {len(concurrent_errors)} 个并发冲突")
    
    def analyze_websocket_logs(self):
        """分析WebSocket专用日志"""
        print("🔍 分析WebSocket专用日志...")
        
        # 1. 静默断开日志分析
        silent_log = self.logs_dir / "websocket_silent_disconnect_20250804.log"
        if silent_log.exists():
            with open(silent_log, 'r', encoding='utf-8') as f:
                silent_content = f.read()
            
            # 提取阻塞时长
            blocking_durations = re.findall(r'silent_duration_seconds.*?(\d+\.\d+)', silent_content)
            if blocking_durations:
                max_blocking = max(float(d) for d in blocking_durations)
                self.diagnosis_results["critical_issues"].append({
                    "type": "WEBSOCKET_DATA_FLOW_BLOCKING",
                    "max_blocking_seconds": max_blocking,
                    "occurrences": len(blocking_durations),
                    "description": f"WebSocket数据流阻塞，最长{max_blocking:.1f}秒",
                    "impact": "实时数据中断，套利机会丢失",
                    "fix_required": "修复WebSocket并发冲突和订阅机制"
                })
        
        # 2. 订阅失败日志分析
        sub_fail_log = self.logs_dir / "websocket_subscription_failure_20250804.log"
        if sub_fail_log.exists():
            with open(sub_fail_log, 'r', encoding='utf-8') as f:
                sub_content = f.read()
            
            # 统计订阅失败次数
            gate_failures = len(re.findall(r'\[gate\].*深度订阅失败', sub_content))
            okx_failures = len(re.findall(r'\[okx\].*订阅批次发送失败', sub_content))
            
            if gate_failures > 0:
                self.diagnosis_results["high_issues"].append({
                    "type": "GATE_SUBSCRIPTION_FAILURES",
                    "count": gate_failures,
                    "description": f"Gate.io订阅失败{gate_failures}次",
                    "impact": "Gate.io数据流不完整",
                    "fix_required": "优化订阅频率，增加重试机制"
                })
            
            if okx_failures > 0:
                self.diagnosis_results["high_issues"].append({
                    "type": "OKX_SUBSCRIPTION_FAILURES", 
                    "count": okx_failures,
                    "description": f"OKX订阅失败{okx_failures}次",
                    "impact": "OKX数据流不完整",
                    "fix_required": "修复WebSocket并发冲突"
                })
        
        print(f"✅ WebSocket日志分析完成")
    
    def check_websocket_implementation(self):
        """检查WebSocket实现代码"""
        print("🔍 检查WebSocket实现代码...")
        
        # 检查OKX WebSocket实现
        okx_ws_file = self.project_root / "websocket" / "okx_ws.py"
        if okx_ws_file.exists():
            with open(okx_ws_file, 'r', encoding='utf-8') as f:
                okx_content = f.read()
            
            # 检查是否存在并发监控任务
            if "_monitor_data_flow" in okx_content:
                self.diagnosis_results["critical_issues"].append({
                    "type": "OKX_CONCURRENT_MONITORING_TASK",
                    "file": "websocket/okx_ws.py",
                    "description": "OKX存在并发数据流监控任务",
                    "impact": "导致WebSocket recv()并发冲突",
                    "fix_required": "移除_monitor_data_flow监控任务"
                })
            
            # 检查是否使用asyncio.gather
            if "asyncio.gather" in okx_content:
                self.diagnosis_results["critical_issues"].append({
                    "type": "OKX_CONCURRENT_SUBSCRIPTION",
                    "file": "websocket/okx_ws.py", 
                    "description": "OKX使用asyncio.gather并发订阅",
                    "impact": "可能导致WebSocket并发冲突",
                    "fix_required": "改为顺序订阅"
                })
        
        print("✅ WebSocket实现检查完成")
    
    def generate_recommendations(self):
        """生成修复建议"""
        print("🎯 生成修复建议...")
        
        # 基于问题严重程度生成建议
        if any(issue["type"] == "OKX_WEBSOCKET_CONCURRENT_CONFLICT" for issue in self.diagnosis_results["critical_issues"]):
            self.diagnosis_results["recommendations"].append({
                "priority": "CRITICAL",
                "action": "立即修复OKX WebSocket并发冲突",
                "steps": [
                    "移除okx_ws.py中的_monitor_data_flow方法",
                    "移除run()方法中的监控任务创建",
                    "确保只有主消息循环调用ws.recv()",
                    "采用Bybit简洁架构模式"
                ]
            })
        
        if any(issue["type"] == "GATE_SUBSCRIPTION_FAILURES" for issue in self.diagnosis_results["high_issues"]):
            self.diagnosis_results["recommendations"].append({
                "priority": "HIGH",
                "action": "优化Gate.io订阅机制",
                "steps": [
                    "降低订阅频率，增加间隔时间",
                    "实施智能重试机制",
                    "添加订阅状态监控",
                    "优化错误处理逻辑"
                ]
            })
        
        if any(issue["type"] == "BYBIT_SYMBOL_FORMAT_ERROR" for issue in self.diagnosis_results["medium_issues"]):
            self.diagnosis_results["recommendations"].append({
                "priority": "MEDIUM", 
                "action": "统一交易对格式处理",
                "steps": [
                    "检查currency_adapter.py中的符号转换",
                    "确保SHIB-USDT正确转换为SHIBUSDT",
                    "验证所有交易所的符号格式一致性",
                    "添加符号格式验证"
                ]
            })
    
    def save_diagnosis_report(self):
        """保存诊断报告"""
        report_file = self.project_root / "diagnostic_scripts" / f"websocket_blocking_diagnosis_{self.diagnosis_results['timestamp']}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 诊断报告已保存: {report_file}")
        return report_file
    
    def print_summary(self):
        """打印诊断摘要"""
        print("\n" + "="*80)
        print("🏛️ WebSocket数据流阻塞问题诊断报告")
        print("="*80)
        
        print(f"\n🚨 严重问题 ({len(self.diagnosis_results['critical_issues'])}个):")
        for issue in self.diagnosis_results["critical_issues"]:
            print(f"  ❌ {issue['type']}: {issue['description']}")
        
        print(f"\n🟡 高级问题 ({len(self.diagnosis_results['high_issues'])}个):")
        for issue in self.diagnosis_results["high_issues"]:
            print(f"  ⚠️ {issue['type']}: {issue['description']}")
        
        print(f"\n🔵 中级问题 ({len(self.diagnosis_results['medium_issues'])}个):")
        for issue in self.diagnosis_results["medium_issues"]:
            print(f"  ℹ️ {issue['type']}: {issue['description']}")
        
        print(f"\n🎯 修复建议 ({len(self.diagnosis_results['recommendations'])}个):")
        for rec in self.diagnosis_results["recommendations"]:
            print(f"  🔧 [{rec['priority']}] {rec['action']}")
        
        # 计算总体健康度
        total_issues = (len(self.diagnosis_results['critical_issues']) + 
                       len(self.diagnosis_results['high_issues']) + 
                       len(self.diagnosis_results['medium_issues']))
        
        if len(self.diagnosis_results['critical_issues']) > 0:
            health_status = "CRITICAL"
        elif len(self.diagnosis_results['high_issues']) > 0:
            health_status = "WARNING"
        elif len(self.diagnosis_results['medium_issues']) > 0:
            health_status = "CAUTION"
        else:
            health_status = "GOOD"
        
        print(f"\n📊 系统健康度: {health_status}")
        print(f"📊 总问题数: {total_issues}")
        print("="*80)
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 启动WebSocket数据流阻塞问题诊断...")
        print(f"📁 项目根目录: {self.project_root}")
        print(f"📁 日志目录: {self.logs_dir}")
        
        # 执行各项检查
        self.analyze_error_logs()
        self.analyze_websocket_logs()
        self.check_websocket_implementation()
        self.generate_recommendations()
        
        # 输出结果
        self.print_summary()
        report_file = self.save_diagnosis_report()
        
        return self.diagnosis_results

def main():
    """主函数"""
    diagnosis = WebSocketBlockingDiagnosis()
    results = diagnosis.run_diagnosis()
    
    # 返回退出码
    if len(results['critical_issues']) > 0:
        sys.exit(1)  # 有严重问题
    elif len(results['high_issues']) > 0:
        sys.exit(2)  # 有高级问题
    else:
        sys.exit(0)  # 正常

if __name__ == "__main__":
    main()
