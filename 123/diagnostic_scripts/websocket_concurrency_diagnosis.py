#!/usr/bin/env python3
"""
🔥 WebSocket并发冲突精确诊断脚本
基于实际日志分析OKX和Gate.io并发问题严重程度
"""

import os
import json
import re
import time
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict

class WebSocketConcurrencyDiagnosis:
    """WebSocket并发冲突诊断器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'log_analysis': {},
            'concurrency_conflicts': {},
            'severity_assessment': {},
            'exchange_comparison': {},
            'critical_errors': [],
            'recommendations': []
        }
        
        # 关键错误模式
        self.error_patterns = {
            'recv_conflict': r'cannot call recv while another coroutine is already waiting',
            'connection_closed': r'Connection is (?:CLOSED|closed)',
            'websocket_error': r'WebSocket.*(?:error|Error|ERROR)',
            'api_limit': r'Too Many Requests|rate limit|频率限制',
            'symbol_invalid': r'symbol.*invalid|invalid.*symbol|unknown currency pair',
            'concurrent_access': r'concurrent|parallel|同时|并发',
            'health_monitor': r'health.*monitor|健康监控',
            'connection_pool': r'connection.*pool|连接池'
        }
    
    def analyze_log_files(self):
        """分析所有WebSocket相关日志文件"""
        log_dir = "/root/myproject/123/70 gate和okx还是数据阻塞/123/logs"
        
        if not os.path.exists(log_dir):
            print(f"⚠️ 日志目录不存在: {log_dir}")
            return
        
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        print(f"📊 发现 {len(log_files)} 个日志文件")
        
        for log_file in log_files:
            log_path = os.path.join(log_dir, log_file)
            self.analyze_single_log_file(log_path, log_file)
    
    def analyze_single_log_file(self, log_path: str, log_file: str):
        """分析单个日志文件"""
        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            exchange = self.identify_exchange(log_file, content)
            
            # 分析并发冲突
            conflicts = self.find_concurrency_conflicts(content)
            
            # 分析错误严重程度
            severity = self.assess_error_severity(content)
            
            # 记录结果
            self.results['log_analysis'][log_file] = {
                'exchange': exchange,
                'file_size': len(content),
                'total_lines': len(content.split('\n')),
                'conflicts': conflicts,
                'severity': severity
            }
            
            if conflicts['recv_conflict_count'] > 0 or conflicts['concurrent_access_count'] > 0:
                self.results['critical_errors'].append({
                    'file': log_file,
                    'exchange': exchange,
                    'recv_conflicts': conflicts['recv_conflict_count'],
                    'concurrent_access': conflicts['concurrent_access_count']
                })
                
            print(f"🔍 {log_file} ({exchange}): recv冲突={conflicts['recv_conflict_count']}, 并发访问={conflicts['concurrent_access_count']}")
            
        except Exception as e:
            print(f"❌ 分析 {log_file} 时出错: {e}")
    
    def identify_exchange(self, log_file: str, content: str) -> str:
        """识别日志文件对应的交易所"""
        if 'okx' in log_file.lower() or 'OKX' in content[:1000]:
            return 'OKX'
        elif 'gate' in log_file.lower() or 'GATE' in content[:1000] or 'Gate' in content[:1000]:
            return 'Gate.io'
        elif 'bybit' in log_file.lower() or 'BYBIT' in content[:1000] or 'Bybit' in content[:1000]:
            return 'Bybit'
        else:
            return 'Unknown'
    
    def find_concurrency_conflicts(self, content: str) -> Dict[str, int]:
        """查找并发冲突模式"""
        conflicts = defaultdict(int)
        
        for pattern_name, pattern in self.error_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            conflicts[f"{pattern_name}_count"] = len(matches)
            
            if matches and pattern_name in ['recv_conflict', 'concurrent_access']:
                # 提取具体错误行
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if re.search(pattern, line, re.IGNORECASE):
                        conflicts[f"{pattern_name}_examples"] = conflicts.get(f"{pattern_name}_examples", [])
                        conflicts[f"{pattern_name}_examples"].append({
                            'line_number': i + 1,
                            'content': line.strip()[:200]  # 限制长度
                        })
                        if len(conflicts[f"{pattern_name}_examples"]) >= 5:  # 最多记录5个例子
                            break
        
        return dict(conflicts)
    
    def assess_error_severity(self, content: str) -> Dict[str, Any]:
        """评估错误严重程度"""
        total_lines = len(content.split('\n'))
        
        # 计算错误率
        error_count = len(re.findall(r'ERROR|Error|error', content))
        warning_count = len(re.findall(r'WARNING|Warning|warning', content))
        
        # 计算WebSocket相关错误
        websocket_errors = len(re.findall(r'WebSocket.*(?:error|Error|ERROR|fail|Fail|FAIL)', content))
        
        # 评估严重程度
        if error_count > total_lines * 0.1:  # 错误率超过10%
            severity_level = "CRITICAL"
        elif error_count > total_lines * 0.05:  # 错误率超过5%
            severity_level = "HIGH"
        elif error_count > total_lines * 0.01:  # 错误率超过1%
            severity_level = "MEDIUM"
        else:
            severity_level = "LOW"
        
        return {
            'severity_level': severity_level,
            'total_lines': total_lines,
            'error_count': error_count,
            'warning_count': warning_count,
            'websocket_errors': websocket_errors,
            'error_rate': error_count / max(total_lines, 1) * 100
        }
    
    def generate_comparison_report(self):
        """生成交易所对比报告"""
        exchanges = {'OKX': [], 'Gate.io': [], 'Bybit': []}
        
        for log_file, analysis in self.results['log_analysis'].items():
            exchange = analysis['exchange']
            if exchange in exchanges:
                exchanges[exchange].append(analysis)
        
        for exchange, analyses in exchanges.items():
            if not analyses:
                continue
                
            total_recv_conflicts = sum(a['conflicts'].get('recv_conflict_count', 0) for a in analyses)
            total_concurrent_access = sum(a['conflicts'].get('concurrent_access_count', 0) for a in analyses)
            avg_error_rate = sum(a['severity']['error_rate'] for a in analyses) / len(analyses)
            
            self.results['exchange_comparison'][exchange] = {
                'log_files_count': len(analyses),
                'total_recv_conflicts': total_recv_conflicts,
                'total_concurrent_access': total_concurrent_access,
                'average_error_rate': round(avg_error_rate, 2),
                'severity_assessment': self.assess_exchange_severity(total_recv_conflicts, total_concurrent_access, avg_error_rate)
            }
    
    def assess_exchange_severity(self, recv_conflicts: int, concurrent_access: int, error_rate: float) -> str:
        """评估单个交易所的严重程度"""
        total_issues = recv_conflicts + concurrent_access
        
        if total_issues > 50 or error_rate > 10:
            return "CRITICAL - 严重并发问题"
        elif total_issues > 10 or error_rate > 5:
            return "HIGH - 明显并发问题"
        elif total_issues > 0 or error_rate > 1:
            return "MEDIUM - 轻微并发问题"
        else:
            return "LOW - 基本正常"
    
    def generate_recommendations(self):
        """生成修复建议"""
        recommendations = []
        
        # 分析最严重的交易所
        most_critical = None
        max_issues = 0
        
        for exchange, data in self.results['exchange_comparison'].items():
            total_issues = data['total_recv_conflicts'] + data['total_concurrent_access']
            if total_issues > max_issues:
                max_issues = total_issues
                most_critical = exchange
        
        if most_critical and max_issues > 0:
            recommendations.append(f"🚨 优先修复 {most_critical}: 发现 {max_issues} 个并发冲突问题")
        
        # 基于错误模式生成建议
        for log_file, analysis in self.results['log_analysis'].items():
            if analysis['conflicts'].get('recv_conflict_count', 0) > 0:
                recommendations.append(f"🔧 {analysis['exchange']} 需要禁用健康监控任务，遵守WebSocket单一消费者原则")
            
            if analysis['conflicts'].get('symbol_invalid_count', 0) > 0:
                recommendations.append(f"🔧 {analysis['exchange']} 需要添加交易对验证机制，过滤不支持的交易对")
        
        self.results['recommendations'] = recommendations
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始WebSocket并发冲突精确诊断...")
        
        # 分析日志文件
        self.analyze_log_files()
        
        # 生成对比报告
        self.generate_comparison_report()
        
        # 生成修复建议
        self.generate_recommendations()
        
        # 输出结果
        self.print_diagnosis_results()
        
        # 保存结果
        self.save_results()
    
    def print_diagnosis_results(self):
        """打印诊断结果"""
        print("\n" + "="*80)
        print("📊 WebSocket并发冲突诊断结果")
        print("="*80)
        
        # 交易所对比
        print("\n🔍 交易所并发问题对比:")
        for exchange, data in self.results['exchange_comparison'].items():
            print(f"  {exchange}:")
            print(f"    - recv()冲突: {data['total_recv_conflicts']} 次")
            print(f"    - 并发访问: {data['total_concurrent_access']} 次") 
            print(f"    - 错误率: {data['average_error_rate']}%")
            print(f"    - 严重程度: {data['severity_assessment']}")
        
        # 最严重的问题
        print(f"\n🚨 发现 {len(self.results['critical_errors'])} 个关键错误:")
        for error in self.results['critical_errors']:
            print(f"  - {error['exchange']}: recv冲突={error['recv_conflicts']}, 并发访问={error['concurrent_access']}")
        
        # 修复建议
        print(f"\n🔧 修复建议 ({len(self.results['recommendations'])} 项):")
        for rec in self.results['recommendations']:
            print(f"  {rec}")
    
    def save_results(self):
        """保存诊断结果"""
        timestamp = int(time.time())
        filename = f"websocket_concurrency_diagnosis_{timestamp}.json"
        filepath = f"/root/myproject/123/70 gate和okx还是数据阻塞/123/logs/{filename}"
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 诊断结果已保存: {filepath}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    diagnosis = WebSocketConcurrencyDiagnosis()
    diagnosis.run_diagnosis()

if __name__ == "__main__":
    main()