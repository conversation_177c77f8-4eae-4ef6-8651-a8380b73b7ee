#!/usr/bin/env python3
"""
WebSocket修复验证脚本
验证所有关键修复是否正确实施

执行: python3 diagnostic_scripts/websocket_fix_verification.py
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WebSocketFixVerification:
    def __init__(self):
        self.project_root = project_root
        self.verification_results = {
            "timestamp": int(time.time()),
            "verification_time": datetime.now().isoformat(),
            "fixes_verified": [],
            "remaining_issues": [],
            "overall_status": "UNKNOWN"
        }
    
    def verify_okx_concurrent_fix(self):
        """验证OKX并发冲突修复"""
        print("🔍 验证OKX WebSocket并发冲突修复...")
        
        okx_ws_file = self.project_root / "websocket" / "okx_ws.py"
        if not okx_ws_file.exists():
            self.verification_results["remaining_issues"].append({
                "type": "FILE_MISSING",
                "description": "okx_ws.py文件不存在"
            })
            return False
        
        with open(okx_ws_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了并发监控任务
        issues = []
        
        if "_monitor_data_flow" in content and "def _monitor_data_flow" in content:
            issues.append("仍存在_monitor_data_flow方法定义")
        
        if "asyncio.gather" in content:
            issues.append("仍使用asyncio.gather并发订阅")
        
        if "create_task.*monitor" in content:
            issues.append("仍创建监控任务")
        
        if issues:
            self.verification_results["remaining_issues"].append({
                "type": "OKX_CONCURRENT_CONFLICT_NOT_FIXED",
                "issues": issues,
                "description": "OKX并发冲突未完全修复"
            })
            print(f"  ❌ 发现问题: {', '.join(issues)}")
            return False
        else:
            self.verification_results["fixes_verified"].append({
                "type": "OKX_CONCURRENT_CONFLICT_FIXED",
                "description": "OKX WebSocket并发冲突已修复",
                "details": "已移除_monitor_data_flow和并发订阅"
            })
            print("  ✅ OKX并发冲突修复验证通过")
            return True
    
    def verify_gate_subscription_fix(self):
        """验证Gate.io订阅机制修复"""
        print("🔍 验证Gate.io订阅机制修复...")
        
        gate_ws_file = self.project_root / "websocket" / "gate_ws.py"
        if not gate_ws_file.exists():
            self.verification_results["remaining_issues"].append({
                "type": "FILE_MISSING",
                "description": "gate_ws.py文件不存在"
            })
            return False
        
        with open(gate_ws_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查订阅间隔优化
        if "await asyncio.sleep(0.1)" in content:
            self.verification_results["fixes_verified"].append({
                "type": "GATE_SUBSCRIPTION_INTERVAL_OPTIMIZED",
                "description": "Gate.io订阅间隔已优化为0.1秒",
                "details": "降低订阅频率，减少失败率"
            })
            print("  ✅ Gate.io订阅间隔优化验证通过")
            return True
        else:
            self.verification_results["remaining_issues"].append({
                "type": "GATE_SUBSCRIPTION_NOT_OPTIMIZED",
                "description": "Gate.io订阅间隔未优化"
            })
            print("  ❌ Gate.io订阅间隔未找到0.1秒设置")
            return False
    
    def verify_symbol_format_fix(self):
        """验证交易对格式修复"""
        print("🔍 验证交易对格式修复...")
        
        env_file = self.project_root / ".env"
        if not env_file.exists():
            self.verification_results["remaining_issues"].append({
                "type": "FILE_MISSING",
                "description": ".env文件不存在"
            })
            return False
        
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了SHIB-USDT
        target_symbols_line = None
        for line in content.split('\n'):
            if line.startswith('TARGET_SYMBOLS='):
                target_symbols_line = line
                break
        
        if target_symbols_line:
            symbols = target_symbols_line.split('=')[1].split(',')
            symbols = [s.strip() for s in symbols if s.strip()]
            
            if "SHIB-USDT" not in symbols:
                self.verification_results["fixes_verified"].append({
                    "type": "UNSUPPORTED_SYMBOLS_REMOVED",
                    "description": "已移除不支持的交易对",
                    "details": f"当前交易对: {', '.join(symbols)}",
                    "removed_symbols": ["SHIB-USDT", "BNB-USDT"]
                })
                print(f"  ✅ 不支持的交易对已移除，当前: {', '.join(symbols)}")
                return True
            else:
                self.verification_results["remaining_issues"].append({
                    "type": "UNSUPPORTED_SYMBOLS_NOT_REMOVED",
                    "description": "SHIB-USDT仍在配置中",
                    "current_symbols": symbols
                })
                print(f"  ❌ SHIB-USDT仍在配置中: {', '.join(symbols)}")
                return False
        else:
            self.verification_results["remaining_issues"].append({
                "type": "TARGET_SYMBOLS_NOT_FOUND",
                "description": ".env中未找到TARGET_SYMBOLS配置"
            })
            print("  ❌ 未找到TARGET_SYMBOLS配置")
            return False
    
    def verify_websocket_architecture(self):
        """验证WebSocket架构一致性"""
        print("🔍 验证WebSocket架构一致性...")
        
        # 检查三个交易所的WebSocket实现
        exchanges = ["okx", "gate", "bybit"]
        architecture_issues = []
        
        for exchange in exchanges:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否使用统一时间戳处理器
                if "self.timestamp_processor" in content:
                    print(f"  ✅ {exchange.upper()}使用统一时间戳处理器")
                else:
                    architecture_issues.append(f"{exchange}未使用统一时间戳处理器")
                
                # 检查是否继承基类
                if "super().run()" in content:
                    print(f"  ✅ {exchange.upper()}正确继承基类")
                else:
                    architecture_issues.append(f"{exchange}未正确继承基类")
        
        if architecture_issues:
            self.verification_results["remaining_issues"].append({
                "type": "ARCHITECTURE_INCONSISTENCY",
                "issues": architecture_issues,
                "description": "WebSocket架构不一致"
            })
            return False
        else:
            self.verification_results["fixes_verified"].append({
                "type": "WEBSOCKET_ARCHITECTURE_CONSISTENT",
                "description": "WebSocket架构一致性验证通过",
                "details": "三交易所都使用统一模块和基类"
            })
            return True
    
    def calculate_overall_status(self):
        """计算总体修复状态"""
        total_fixes = len(self.verification_results["fixes_verified"])
        total_issues = len(self.verification_results["remaining_issues"])
        
        if total_issues == 0:
            self.verification_results["overall_status"] = "FULLY_FIXED"
        elif total_fixes > total_issues:
            self.verification_results["overall_status"] = "MOSTLY_FIXED"
        elif total_fixes == total_issues:
            self.verification_results["overall_status"] = "PARTIALLY_FIXED"
        else:
            self.verification_results["overall_status"] = "NEEDS_MORE_WORK"
        
        return self.verification_results["overall_status"]
    
    def save_verification_report(self):
        """保存验证报告"""
        report_file = self.project_root / "diagnostic_scripts" / f"websocket_fix_verification_{self.verification_results['timestamp']}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 验证报告已保存: {report_file}")
        return report_file
    
    def print_summary(self):
        """打印验证摘要"""
        print("\n" + "="*80)
        print("🏛️ WebSocket修复验证报告")
        print("="*80)
        
        print(f"\n✅ 已修复问题 ({len(self.verification_results['fixes_verified'])}个):")
        for fix in self.verification_results["fixes_verified"]:
            print(f"  • {fix['description']}")
        
        print(f"\n❌ 剩余问题 ({len(self.verification_results['remaining_issues'])}个):")
        for issue in self.verification_results["remaining_issues"]:
            print(f"  • {issue['description']}")
        
        status = self.verification_results["overall_status"]
        status_emoji = {
            "FULLY_FIXED": "🎉",
            "MOSTLY_FIXED": "✅", 
            "PARTIALLY_FIXED": "⚠️",
            "NEEDS_MORE_WORK": "❌"
        }
        
        print(f"\n📊 总体修复状态: {status_emoji.get(status, '❓')} {status}")
        print("="*80)
    
    def run_verification(self):
        """运行完整验证"""
        print("🚀 启动WebSocket修复验证...")
        print(f"📁 项目根目录: {self.project_root}")
        
        # 执行各项验证
        verifications = [
            self.verify_okx_concurrent_fix,
            self.verify_gate_subscription_fix,
            self.verify_symbol_format_fix,
            self.verify_websocket_architecture
        ]
        
        success_count = 0
        for verification in verifications:
            if verification():
                success_count += 1
        
        # 计算总体状态
        overall_status = self.calculate_overall_status()
        
        # 输出结果
        self.print_summary()
        report_file = self.save_verification_report()
        
        print(f"\n🎯 验证完成: {success_count}/{len(verifications)} 项通过")
        
        return self.verification_results

def main():
    """主函数"""
    verification = WebSocketFixVerification()
    results = verification.run_verification()
    
    # 返回退出码
    status = results["overall_status"]
    if status == "FULLY_FIXED":
        sys.exit(0)  # 完全修复
    elif status == "MOSTLY_FIXED":
        sys.exit(1)  # 大部分修复
    else:
        sys.exit(2)  # 需要更多工作

if __name__ == "__main__":
    main()
