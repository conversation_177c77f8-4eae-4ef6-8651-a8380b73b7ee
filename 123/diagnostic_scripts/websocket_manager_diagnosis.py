#!/usr/bin/env python3
"""
WebSocket管理器connection_pool_manager属性缺失问题精确诊断脚本
2025-08-03

目标：精确定位WebSocketManager中connection_pool_manager属性缺失的根本原因
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def diagnose_websocket_manager_attributes():
    """诊断WebSocketManager类的属性定义问题"""
    print("🔍 开始诊断WebSocketManager属性定义...")
    
    issues = []
    
    try:
        # 1. 检查WebSocketManager类定义
        from websocket.ws_manager import WebSocketManager
        
        # 创建实例
        ws_manager = WebSocketManager()
        
        # 2. 检查属性存在性
        print("\n📋 检查WebSocketManager实例属性:")
        
        # 检查私有属性
        has_private_attr = hasattr(ws_manager, '_connection_pool_manager')
        print(f"   _connection_pool_manager (私有): {'✅ 存在' if has_private_attr else '❌ 不存在'}")
        
        # 检查公共属性
        has_public_attr = hasattr(ws_manager, 'connection_pool_manager')
        print(f"   connection_pool_manager (公共): {'✅ 存在' if has_public_attr else '❌ 不存在'}")
        
        # 3. 检查属性值
        if has_private_attr:
            private_value = getattr(ws_manager, '_connection_pool_manager', None)
            print(f"   _connection_pool_manager 值: {type(private_value)} = {private_value}")
        
        if has_public_attr:
            public_value = getattr(ws_manager, 'connection_pool_manager', None)
            print(f"   connection_pool_manager 值: {type(public_value)} = {public_value}")
        
        # 4. 检查代码中的访问方式
        print("\n🔍 检查代码中的属性访问方式:")
        
        # 读取ws_manager.py文件
        ws_manager_file = project_root / "websocket" / "ws_manager.py"
        with open(ws_manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有connection_pool_manager的使用
        lines = content.split('\n')
        private_access_lines = []
        public_access_lines = []
        
        for i, line in enumerate(lines, 1):
            if '_connection_pool_manager' in line and 'self._connection_pool_manager' in line:
                private_access_lines.append((i, line.strip()))
            elif 'connection_pool_manager' in line and 'self.connection_pool_manager' in line:
                public_access_lines.append((i, line.strip()))
        
        print(f"   私有属性访问 (_connection_pool_manager): {len(private_access_lines)} 处")
        for line_num, line in private_access_lines[:5]:  # 只显示前5个
            print(f"     第{line_num}行: {line}")
        
        print(f"   公共属性访问 (connection_pool_manager): {len(public_access_lines)} 处")
        for line_num, line in public_access_lines[:5]:  # 只显示前5个
            print(f"     第{line_num}行: {line}")
        
        # 5. 分析问题
        if len(public_access_lines) > 0 and not has_public_attr:
            issues.append({
                "type": "CRITICAL",
                "description": "代码中访问公共属性connection_pool_manager，但实例中不存在此属性",
                "private_accesses": len(private_access_lines),
                "public_accesses": len(public_access_lines),
                "solution": "需要添加@property装饰器或直接定义公共属性"
            })
        
        # 6. 检查统一连接池管理器是否可用
        print("\n🔍 检查统一连接池管理器可用性:")
        try:
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            pool_manager = get_connection_pool_manager()
            print(f"   统一连接池管理器: ✅ 可用 ({type(pool_manager)})")
        except Exception as e:
            print(f"   统一连接池管理器: ❌ 不可用 - {e}")
            issues.append({
                "type": "HIGH",
                "description": f"统一连接池管理器导入失败: {e}",
                "solution": "检查unified_connection_pool_manager.py文件"
            })
        
        return {
            "has_private_attr": has_private_attr,
            "has_public_attr": has_public_attr,
            "private_accesses": len(private_access_lines),
            "public_accesses": len(public_access_lines),
            "issues": issues
        }
        
    except Exception as e:
        issues.append({
            "type": "CRITICAL",
            "description": f"WebSocketManager诊断失败: {e}",
            "solution": "检查WebSocketManager类定义"
        })
        return {"issues": issues}

def generate_fix_recommendations(diagnosis_result):
    """生成修复建议"""
    print("\n🔧 修复建议:")
    
    if not diagnosis_result.get("has_public_attr") and diagnosis_result.get("public_accesses", 0) > 0:
        print("   1. 添加connection_pool_manager属性访问器:")
        print("      @property")
        print("      def connection_pool_manager(self):")
        print("          return self._connection_pool_manager")
        print()
        
        print("   2. 或者直接在__init__中定义公共属性:")
        print("      self.connection_pool_manager = self._connection_pool_manager")
        print()
    
    for issue in diagnosis_result.get("issues", []):
        print(f"   🚨 {issue['type']}: {issue['description']}")
        print(f"      解决方案: {issue['solution']}")
        print()

def main():
    """主函数"""
    print("🔍 WebSocket管理器connection_pool_manager属性缺失问题精确诊断")
    print("=" * 80)
    
    # 执行诊断
    diagnosis_result = diagnose_websocket_manager_attributes()
    
    # 生成修复建议
    generate_fix_recommendations(diagnosis_result)
    
    # 保存诊断结果
    timestamp = int(time.time())
    result_file = project_root / "diagnostic_scripts" / f"websocket_manager_diagnosis_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(diagnosis_result, f, indent=2, ensure_ascii=False)
    
    print(f"📊 诊断结果已保存到: {result_file}")
    
    # 总结
    issues_count = len(diagnosis_result.get("issues", []))
    if issues_count == 0:
        print("\n✅ 诊断完成：未发现问题")
    else:
        print(f"\n🚨 诊断完成：发现 {issues_count} 个问题")
        critical_count = sum(1 for issue in diagnosis_result.get("issues", []) if issue["type"] == "CRITICAL")
        if critical_count > 0:
            print(f"   其中 {critical_count} 个为CRITICAL级别问题，需要立即修复")

if __name__ == "__main__":
    main()
