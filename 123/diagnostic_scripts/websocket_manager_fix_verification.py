#!/usr/bin/env python3
"""
WebSocket管理器connection_pool_manager属性修复验证脚本
2025-08-03

目标：验证WebSocketManager中connection_pool_manager属性修复效果
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def verify_websocket_manager_fix():
    """验证WebSocketManager修复效果"""
    print("🔍 开始验证WebSocketManager修复效果...")
    
    verification_results = {
        "fix_successful": False,
        "tests": [],
        "issues": []
    }
    
    try:
        # 1. 创建WebSocketManager实例
        from websocket.ws_manager import WebSocketManager
        ws_manager = WebSocketManager()
        
        # 2. 测试私有属性访问
        test_private = {
            "name": "私有属性_connection_pool_manager访问",
            "passed": False,
            "details": ""
        }
        
        try:
            private_attr = ws_manager._connection_pool_manager
            test_private["passed"] = private_attr is not None
            test_private["details"] = f"类型: {type(private_attr)}"
        except AttributeError as e:
            test_private["details"] = f"错误: {e}"
        
        verification_results["tests"].append(test_private)
        
        # 3. 测试公共属性访问（修复后应该可用）
        test_public = {
            "name": "公共属性connection_pool_manager访问",
            "passed": False,
            "details": ""
        }
        
        try:
            public_attr = ws_manager.connection_pool_manager
            test_public["passed"] = public_attr is not None
            test_public["details"] = f"类型: {type(public_attr)}"
        except AttributeError as e:
            test_public["details"] = f"错误: {e}"
            verification_results["issues"].append({
                "type": "CRITICAL",
                "description": "公共属性connection_pool_manager仍然不可访问",
                "error": str(e)
            })
        
        verification_results["tests"].append(test_public)
        
        # 4. 测试属性一致性
        test_consistency = {
            "name": "私有和公共属性一致性",
            "passed": False,
            "details": ""
        }
        
        try:
            if test_private["passed"] and test_public["passed"]:
                private_attr = ws_manager._connection_pool_manager
                public_attr = ws_manager.connection_pool_manager
                is_same = private_attr is public_attr
                test_consistency["passed"] = is_same
                test_consistency["details"] = f"是否为同一对象: {is_same}"
            else:
                test_consistency["details"] = "无法测试，因为某个属性不可访问"
        except Exception as e:
            test_consistency["details"] = f"错误: {e}"
        
        verification_results["tests"].append(test_consistency)
        
        # 5. 测试连接池管理器方法可用性
        test_methods = {
            "name": "连接池管理器方法可用性",
            "passed": False,
            "details": ""
        }
        
        try:
            if test_public["passed"]:
                pool_manager = ws_manager.connection_pool_manager
                # 检查关键方法
                methods_to_check = ['create_connection', 'start_monitoring', 'stop_monitoring']
                available_methods = []
                
                for method_name in methods_to_check:
                    if hasattr(pool_manager, method_name):
                        available_methods.append(method_name)
                
                test_methods["passed"] = len(available_methods) == len(methods_to_check)
                test_methods["details"] = f"可用方法: {available_methods}"
            else:
                test_methods["details"] = "无法测试，因为connection_pool_manager不可访问"
        except Exception as e:
            test_methods["details"] = f"错误: {e}"
        
        verification_results["tests"].append(test_methods)
        
        # 6. 模拟原始错误场景
        test_original_error = {
            "name": "模拟原始错误场景",
            "passed": False,
            "details": ""
        }
        
        try:
            # 尝试访问connection_pool_manager.create_connection
            pool_manager = ws_manager.connection_pool_manager
            create_connection_method = getattr(pool_manager, 'create_connection', None)
            
            if create_connection_method and callable(create_connection_method):
                test_original_error["passed"] = True
                test_original_error["details"] = "connection_pool_manager.create_connection方法可访问"
            else:
                test_original_error["details"] = "create_connection方法不可用"
        except AttributeError as e:
            test_original_error["details"] = f"仍然出现AttributeError: {e}"
            verification_results["issues"].append({
                "type": "CRITICAL", 
                "description": "修复后仍然出现原始错误",
                "error": str(e)
            })
        except Exception as e:
            test_original_error["details"] = f"其他错误: {e}"
        
        verification_results["tests"].append(test_original_error)
        
        # 7. 计算总体修复成功率
        passed_tests = sum(1 for test in verification_results["tests"] if test["passed"])
        total_tests = len(verification_results["tests"])
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        verification_results["fix_successful"] = success_rate >= 80  # 80%以上认为修复成功
        verification_results["success_rate"] = success_rate
        verification_results["passed_tests"] = passed_tests
        verification_results["total_tests"] = total_tests
        
        return verification_results
        
    except Exception as e:
        verification_results["issues"].append({
            "type": "CRITICAL",
            "description": f"验证过程失败: {e}",
            "error": str(e)
        })
        return verification_results

def print_verification_results(results):
    """打印验证结果"""
    print("\n📊 修复验证结果:")
    print("=" * 60)
    
    # 总体结果
    if results["fix_successful"]:
        print(f"✅ 修复成功！成功率: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})")
    else:
        print(f"❌ 修复失败！成功率: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})")
    
    print("\n🧪 详细测试结果:")
    for i, test in enumerate(results["tests"], 1):
        status = "✅ 通过" if test["passed"] else "❌ 失败"
        print(f"   {i}. {test['name']}: {status}")
        print(f"      详情: {test['details']}")
    
    # 问题列表
    if results["issues"]:
        print(f"\n🚨 发现 {len(results['issues'])} 个问题:")
        for i, issue in enumerate(results["issues"], 1):
            print(f"   {i}. {issue['type']}: {issue['description']}")
            if 'error' in issue:
                print(f"      错误: {issue['error']}")

def main():
    """主函数"""
    print("🔧 WebSocket管理器connection_pool_manager属性修复验证")
    print("=" * 80)
    
    # 执行验证
    results = verify_websocket_manager_fix()
    
    # 打印结果
    print_verification_results(results)
    
    # 保存结果
    timestamp = int(time.time())
    result_file = project_root / "diagnostic_scripts" / f"websocket_manager_fix_verification_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 验证结果已保存到: {result_file}")
    
    # 总结
    if results["fix_successful"]:
        print("\n🎉 修复验证通过！WebSocket管理器connection_pool_manager属性问题已解决")
    else:
        print("\n⚠️ 修复验证失败！需要进一步检查和修复")

if __name__ == "__main__":
    main()
