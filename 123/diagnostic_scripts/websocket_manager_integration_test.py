#!/usr/bin/env python3
"""
WebSocket管理器集成测试脚本
2025-08-03

目标：测试修复后的WebSocket管理器是否能正常初始化和工作
"""

import sys
import os
import asyncio
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_websocket_manager_integration():
    """测试WebSocket管理器集成功能"""
    print("🔍 开始WebSocket管理器集成测试...")
    
    test_results = {
        "overall_success": False,
        "tests": [],
        "issues": []
    }
    
    try:
        # 1. 测试WebSocket管理器创建
        test_creation = {
            "name": "WebSocket管理器创建",
            "passed": False,
            "details": ""
        }
        
        try:
            from websocket.ws_manager import WebSocketManager
            ws_manager = WebSocketManager()
            test_creation["passed"] = True
            test_creation["details"] = "WebSocket管理器创建成功"
        except Exception as e:
            test_creation["details"] = f"创建失败: {e}"
            test_results["issues"].append({
                "type": "CRITICAL",
                "description": f"WebSocket管理器创建失败: {e}"
            })
        
        test_results["tests"].append(test_creation)
        
        if not test_creation["passed"]:
            return test_results
        
        # 2. 测试connection_pool_manager属性访问
        test_pool_access = {
            "name": "连接池管理器属性访问",
            "passed": False,
            "details": ""
        }
        
        try:
            pool_manager = ws_manager.connection_pool_manager
            test_pool_access["passed"] = pool_manager is not None
            test_pool_access["details"] = f"连接池管理器类型: {type(pool_manager)}"
        except AttributeError as e:
            test_pool_access["details"] = f"属性访问失败: {e}"
            test_results["issues"].append({
                "type": "CRITICAL",
                "description": f"connection_pool_manager属性仍然不可访问: {e}"
            })
        except Exception as e:
            test_pool_access["details"] = f"其他错误: {e}"
        
        test_results["tests"].append(test_pool_access)
        
        # 3. 测试添加交易对
        test_add_symbols = {
            "name": "添加交易对",
            "passed": False,
            "details": ""
        }
        
        try:
            test_symbols = ["BTC-USDT", "ETH-USDT"]
            ws_manager.add_symbols(test_symbols)
            test_add_symbols["passed"] = ws_manager.symbols == test_symbols
            test_add_symbols["details"] = f"交易对设置成功: {ws_manager.symbols}"
        except Exception as e:
            test_add_symbols["details"] = f"添加交易对失败: {e}"
        
        test_results["tests"].append(test_add_symbols)
        
        # 4. 测试客户端初始化（不实际连接）
        test_client_init = {
            "name": "客户端初始化准备",
            "passed": False,
            "details": ""
        }
        
        try:
            # 检查是否可以调用initialize_clients方法
            exchanges_config = [
                {"name": "gate", "spot": True, "futures": False},  # 只测试现货，减少复杂性
            ]
            
            # 不实际调用，只检查方法是否存在和可调用
            init_method = getattr(ws_manager, 'initialize_clients', None)
            if init_method and callable(init_method):
                test_client_init["passed"] = True
                test_client_init["details"] = "initialize_clients方法可用"
            else:
                test_client_init["details"] = "initialize_clients方法不可用"
        except Exception as e:
            test_client_init["details"] = f"客户端初始化准备失败: {e}"
        
        test_results["tests"].append(test_client_init)
        
        # 5. 测试连接池管理器方法调用
        test_pool_methods = {
            "name": "连接池管理器方法调用",
            "passed": False,
            "details": ""
        }
        
        try:
            if test_pool_access["passed"]:
                pool_manager = ws_manager.connection_pool_manager
                
                # 测试关键方法是否存在
                methods_to_test = ['create_connection', 'start_monitoring', 'stop_monitoring']
                available_methods = []
                
                for method_name in methods_to_test:
                    method = getattr(pool_manager, method_name, None)
                    if method and callable(method):
                        available_methods.append(method_name)
                
                test_pool_methods["passed"] = len(available_methods) == len(methods_to_test)
                test_pool_methods["details"] = f"可用方法: {available_methods}"
            else:
                test_pool_methods["details"] = "无法测试，连接池管理器不可访问"
        except Exception as e:
            test_pool_methods["details"] = f"方法调用测试失败: {e}"
        
        test_results["tests"].append(test_pool_methods)
        
        # 6. 计算总体成功率
        passed_tests = sum(1 for test in test_results["tests"] if test["passed"])
        total_tests = len(test_results["tests"])
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        test_results["overall_success"] = success_rate >= 80  # 80%以上认为成功
        test_results["success_rate"] = success_rate
        test_results["passed_tests"] = passed_tests
        test_results["total_tests"] = total_tests
        
        return test_results
        
    except Exception as e:
        test_results["issues"].append({
            "type": "CRITICAL",
            "description": f"集成测试过程失败: {e}",
            "error": str(e)
        })
        return test_results

def print_test_results(results):
    """打印测试结果"""
    print("\n📊 WebSocket管理器集成测试结果:")
    print("=" * 60)
    
    # 总体结果
    if results["overall_success"]:
        print(f"✅ 集成测试成功！成功率: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})")
    else:
        print(f"❌ 集成测试失败！成功率: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})")
    
    print("\n🧪 详细测试结果:")
    for i, test in enumerate(results["tests"], 1):
        status = "✅ 通过" if test["passed"] else "❌ 失败"
        print(f"   {i}. {test['name']}: {status}")
        print(f"      详情: {test['details']}")
    
    # 问题列表
    if results["issues"]:
        print(f"\n🚨 发现 {len(results['issues'])} 个问题:")
        for i, issue in enumerate(results["issues"], 1):
            print(f"   {i}. {issue['type']}: {issue['description']}")

async def main():
    """主函数"""
    print("🔧 WebSocket管理器集成测试")
    print("=" * 80)
    
    # 执行测试
    results = await test_websocket_manager_integration()
    
    # 打印结果
    print_test_results(results)
    
    # 保存结果
    timestamp = int(time.time())
    result_file = project_root / "diagnostic_scripts" / f"websocket_manager_integration_test_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 测试结果已保存到: {result_file}")
    
    # 总结
    if results["overall_success"]:
        print("\n🎉 WebSocket管理器集成测试通过！修复成功，系统可以正常工作")
    else:
        print("\n⚠️ WebSocket管理器集成测试失败！需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
