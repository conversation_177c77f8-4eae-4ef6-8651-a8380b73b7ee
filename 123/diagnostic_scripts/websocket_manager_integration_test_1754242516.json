{"overall_success": true, "tests": [{"name": "WebSocket管理器创建", "passed": true, "details": "WebSocket管理器创建成功"}, {"name": "连接池管理器属性访问", "passed": true, "details": "连接池管理器类型: <class 'websocket.unified_connection_pool_manager.UnifiedConnectionPoolManager'>"}, {"name": "添加交易对", "passed": true, "details": "交易对设置成功: ['BTC-USDT', 'ETH-USDT']"}, {"name": "客户端初始化准备", "passed": true, "details": "initialize_clients方法可用"}, {"name": "连接池管理器方法调用", "passed": true, "details": "可用方法: ['create_connection', 'start_monitoring', 'stop_monitoring']"}], "issues": [], "success_rate": 100.0, "passed_tests": 5, "total_tests": 5}