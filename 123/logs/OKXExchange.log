2025-08-04 18:42:40.110 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 1次/秒
2025-08-04 18:42:40 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-04 18:42:40 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-04 18:42:40 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-04 18:42:40 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-04 18:42:40.110 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=1次/秒
2025-08-04 18:42:40 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-04 18:42:40 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-04 18:42:40 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-04 18:42:40 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-04 18:42:40.733 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:42:41.230 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:42:41.230 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:42:41.732 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:42:41.732 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:42:41 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-04 18:42:41.732 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:42:42.231 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:42:42.231 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:42:42.733 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:42:42.733 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:42:42 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-04 18:42:42.733 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:42:43.234 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:42:43.234 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:42:43.736 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:42:43.736 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:42:43 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-04 18:42:43.737 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:42:44.227 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:42:44.227 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:42:44.734 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:42:44.734 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:42:44 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-04 18:42:44 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-04 18:42:45.232 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 18:42:45.239 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 18:42:45.254 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 18:42:45.260 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 18:42:45.260 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 18:42:45.267 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 18:42:45.297 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 18:43:10.490 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:43:10.490 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:43:11.483 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:43:11.484 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:43:12.488 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:43:12.488 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:43:13.492 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:43:13.492 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:43:14.478 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:43:14.479 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:43:15.488 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:43:15.488 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:43:58.471 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 18:44:03.550 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 18:44:05.043 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 18:44:05.545 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 18:44:05.548 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 18:44:05.560 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 18:44:07.628 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 18:44:09.062 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.063 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SHIB-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.065 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BNB-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 18:44:09.140 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:09.140 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:09.637 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:09.637 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:09.638 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-04 18:44:09.638 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-04 18:44:09.638 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-04 18:44:09.638 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-04 18:44:09.638 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-04 18:44:09.638 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-04 18:44:09.639 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-04 18:44:09.639 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-04 18:44:09 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-04 18:44:09.641 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:09.641 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:09.642 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:09.642 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:09.643 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:09.643 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:09.643 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:09.645 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:09.686 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-04 18:44:09.687 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-04 18:44:09.687 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-04 18:44:09.687 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-04 18:44:09.687 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-04 18:44:09.687 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-04 18:44:09.688 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-04 18:44:09.688 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-04 18:44:09 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-04 18:44:10.145 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:10.145 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:10.147 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:10.147 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:10.151 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:10.151 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:10.155 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:10.155 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:11.723 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:11.723 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:12.215 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 18:44:12.215 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 18:44:12.232 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:12.232 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:12.717 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 18:44:12.717 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 18:44:30.973 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 18:44:31.470 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 18:44:31.472 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 18:44:31.475 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 18:44:31.479 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 18:44:31.479 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 18:44:31.498 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 18:44:38.758 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 18:44:39.259 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 18:44:39.261 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 18:44:39.262 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 18:44:39.266 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 18:44:39.267 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 18:44:39.269 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
