"""
🔥 **已废弃**: 增强的WebSocket客户端基类
🔥 **统一整合**: 功能已集成到ws_client.py和unified_connection_pool_manager.py

此文件保留用于向后兼容，新代码请使用：
- websocket.ws_client.WebSocketClient (基础WebSocket客户端)
- websocket.unified_connection_pool_manager.UnifiedConnectionPoolManager (连接池管理)

🔥 **重构说明**:
1. 连接池管理 → unified_connection_pool_manager.py
2. 智能重连机制 → ws_client.py + connection_pool_manager
3. 多路径备用方案 → connection_pool_manager.py (端点管理)
4. 连接质量监控 → connection_pool_manager.py (质量评估)
5. 数据缓冲和恢复 → connection_pool_manager.py (数据缓冲)
"""

import logging
from websocket.ws_client import WebSocketClient
from websocket.unified_connection_pool_manager import get_connection_pool_manager

logger = logging.getLogger(__name__)

class EnhancedWebSocketClientBase(WebSocketClient):
    """🔥 **兼容性包装器**: 继承基础WebSocketClient，保持向后兼容
    
    ⚠️ **已废弃**: 请直接使用 WebSocketClient
    
    所有增强功能已集成到基础客户端和连接池管理器中。
    """
    
    def __init__(self, exchange: str, market_type: str = "spot"):
        """
        兼容性构造函数
        
        Args:
            exchange: 交易所名称
            market_type: 市场类型 (spot/futures)
        """
        # 🔥 **统一整合**: 调用基础WebSocketClient构造函数
        super().__init__(exchange_name=exchange, settings=None)
        
        self.market_type = market_type
        
        # 🔥 **兼容性**: 保留原有属性映射
        self.exchange = exchange
        self.connected = False
        self.subscriptions = {}
        
        # 🔥 **统一日志**: 使用统一日志系统
        logger.warning(f"⚠️ EnhancedWebSocketClientBase已废弃，建议使用WebSocketClient")
        logger.info(f"创建兼容性WebSocket客户端: {exchange} {market_type}")
    
    def set_symbols(self, symbols):
        """兼容性方法：设置交易对"""
        self.symbols = symbols
        logger.info(f"设置交易对: {symbols}")
    
    def register_callback(self, event_type: str, callback):
        """兼容性方法：注册回调"""
        super().register_callback(event_type, callback)
    
    async def emit_event(self, event_type: str, data):
        """兼容性方法：触发事件"""
        super().emit(event_type, data)


# 🔥 **兼容性函数**: 保持向后兼容
def create_enhanced_websocket_client(exchange: str, market_type: str = "spot"):
    """
    🔥 **已废弃**: 创建增强WebSocket客户端
    
    请使用: WebSocketClient(exchange_name=exchange)
    """
    logger.warning("⚠️ create_enhanced_websocket_client已废弃，请使用WebSocketClient")
    return EnhancedWebSocketClientBase(exchange, market_type)