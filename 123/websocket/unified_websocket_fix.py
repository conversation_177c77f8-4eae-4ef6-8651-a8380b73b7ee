#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞统一修复方案
基于深度分析结果，实施三交易所一致的修复策略
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

class UnifiedWebSocketFixStrategy:
    """统一WebSocket修复策略"""
    
    # 🔥 **官方API规范统一配置参数** - 基于三交易所官方文档精确优化
    # Gate.io: 需要逐个订阅，基于官方文档API规则，使用适度间隔
    GATE_SUBSCRIPTION_INTERVAL = 0.1     # Gate.io优化为100ms间隔，平衡效率和稳定性
    # OKX: 3 requests/second (per IP) + 480 subscriptions/hour限制
    OKX_BATCH_SIZE = 8                   # OKX批量大小8个，平衡效率和稳定性
    OKX_BATCH_INTERVAL = 0.35            # OKX批次间350ms，精确符合3 requests/second限制
    # Bybit: 600 requests/5s per IP，即平均0.0083s/request
    BYBIT_SUBSCRIPTION_INTERVAL = 0.1    # Bybit通用100ms间隔
    
    # 🔥 **统一通用参数** - 三交易所通用设置
    UNIFIED_RECONNECT_DELAY = 2.0        # 重连延迟（秒）
    UNIFIED_HEARTBEAT_INTERVAL = 20      # 心跳间隔（秒）
    UNIFIED_DATA_TIMEOUT = 30            # 数据超时阈值（秒）
    
    @staticmethod
    def get_optimized_subscription_config() -> Dict[str, Any]:
        """获取基于官方API规范优化后的订阅配置"""
        return {
            # Gate.io特定配置
            "gate_subscription_interval": UnifiedWebSocketFixStrategy.GATE_SUBSCRIPTION_INTERVAL,
            # OKX特定配置
            "okx_batch_size": UnifiedWebSocketFixStrategy.OKX_BATCH_SIZE,
            "okx_batch_interval": UnifiedWebSocketFixStrategy.OKX_BATCH_INTERVAL,
            # Bybit特定配置
            "bybit_subscription_interval": UnifiedWebSocketFixStrategy.BYBIT_SUBSCRIPTION_INTERVAL,
            # 通用配置
            "reconnect_delay": UnifiedWebSocketFixStrategy.UNIFIED_RECONNECT_DELAY,
            "heartbeat_interval": UnifiedWebSocketFixStrategy.UNIFIED_HEARTBEAT_INTERVAL,
            "data_timeout": UnifiedWebSocketFixStrategy.UNIFIED_DATA_TIMEOUT
        }


class WebSocketClientFixer:
    """WebSocket客户端修复器基类"""
    
    def __init__(self, exchange_name: str):
        self.exchange_name = exchange_name
        self.config = UnifiedWebSocketFixStrategy.get_optimized_subscription_config()
        self.logger = logging.getLogger(f"websocket_fixer.{exchange_name}")
        
        # 🔥 集成增强的阻塞追踪器
        try:
            from websocket.enhanced_blocking_tracker import get_blocking_tracker
            self.blocking_tracker = get_blocking_tracker()
        except ImportError:
            self.blocking_tracker = None
            
    def log_data_received(self, market_type: str, symbol: str, data: Dict = None):
        """记录数据接收 - 统一接口"""
        if self.blocking_tracker:
            self.blocking_tracker.update_exchange_metrics(
                self.exchange_name, market_type, symbol, data
            )
            
    def log_subscription_attempt(self, market_type: str, symbols: List[str], 
                               success: bool, details: Dict = None):
        """记录订阅尝试 - 统一接口"""
        if self.blocking_tracker:
            self.blocking_tracker.log_subscription_attempt(
                self.exchange_name, market_type, symbols, success, details
            )
            
    def log_rate_limit_violation(self, market_type: str, violation_type: str, 
                               details: Dict = None):
        """记录限速违规 - 统一接口"""
        if self.blocking_tracker:
            self.blocking_tracker.log_rate_limit_violation(
                self.exchange_name, market_type, violation_type, details
            )


class GateWebSocketFixer(WebSocketClientFixer):
    """Gate.io WebSocket修复器"""
    
    def __init__(self):
        super().__init__("gate")
        
    def get_fixed_subscription_params(self, market_type: str, symbol: str) -> Dict[str, Any]:
        """获取修复后的Gate.io订阅参数"""
        # 🔥 修复：基于官方SDK使用正确的2参数格式
        if market_type == "spot":
            return {
                "time": int(time.time()),
                "channel": "spot.order_book",
                "event": "subscribe", 
                "payload": [symbol, "100ms"]  # 🔥 修复：只用2个参数
            }
        else:  # futures
            return {
                "time": int(time.time()),
                "channel": "futures.order_book",
                "event": "subscribe",
                "payload": [symbol]  # 🔥 修复：期货只需要symbol
            }
            
    async def fixed_subscribe_with_rate_control(self, client, symbols: List[str], 
                                              market_type: str) -> bool:
        """修复后的Gate.io订阅方法 - 统一速率控制"""
        success_count = 0
        
        self.logger.info(f"🔧 Gate.io修复订阅开始: {len(symbols)}个交易对")
        
        for i, symbol in enumerate(symbols):
            try:
                # 获取修复后的订阅参数
                sub_msg = self.get_fixed_subscription_params(market_type, symbol)
                
                self.logger.debug(f"🔧 Gate.io修复订阅 {i+1}/{len(symbols)}: {symbol}")
                
                # 发送订阅
                success = await client.send(sub_msg)
                
                if success:
                    success_count += 1
                    self.log_data_received(market_type, symbol, {"subscription": "success"})
                else:
                    self.log_subscription_attempt(market_type, [symbol], False, 
                                                {"error": "send_failed"})
                
                # 🔥 关键修复：统一间隔，从0.02秒改为0.15秒
                if i < len(symbols) - 1:  # 最后一个不需要等待
                    await asyncio.sleep(self.config["subscription_interval"])
                    
            except Exception as e:
                self.logger.error(f"Gate.io订阅 {symbol} 失败: {e}")
                self.log_subscription_attempt(market_type, [symbol], False, 
                                            {"error": str(e)})
                
        # 记录整体订阅结果
        self.log_subscription_attempt(market_type, symbols, success_count > 0, 
                                    {"success_count": success_count, "total": len(symbols)})
        
        self.logger.info(f"✅ Gate.io修复订阅完成: {success_count}/{len(symbols)} 成功")
        return success_count > 0


class OKXWebSocketFixer(WebSocketClientFixer):
    """OKX WebSocket修复器"""
    
    def __init__(self):
        super().__init__("okx")
        
    async def fixed_batch_subscribe_with_rate_control(self, client, symbols: List[str], 
                                                    market_type: str) -> bool:
        """修复后的OKX批量订阅方法 - 增加速率控制"""
        success_count = 0
        batch_size = self.config["batch_size"]  # 🔥 修复：减少批次大小从10到5
        
        self.logger.info(f"🔧 OKX修复批量订阅开始: {len(symbols)}个交易对，批次大小{batch_size}")
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]
            batch_num = i//batch_size + 1
            
            try:
                # 构建批量订阅消息
                args = []
                inst_type = "SPOT" if market_type == "spot" else "SWAP"
                
                for symbol in batch_symbols:
                    args.append({
                        "channel": "books",
                        "instId": symbol
                    })
                
                sub_msg = {
                    "op": "subscribe", 
                    "args": args
                }
                
                self.logger.debug(f"🔧 OKX修复批量订阅 批次{batch_num}: {len(batch_symbols)}个交易对")
                
                # 发送批量订阅
                success = await client.send(sub_msg)
                
                if success:
                    success_count += len(batch_symbols)
                    for symbol in batch_symbols:
                        self.log_data_received(market_type, symbol, {"batch_subscription": "success"})
                else:
                    self.log_subscription_attempt(market_type, batch_symbols, False,
                                                {"batch_num": batch_num, "error": "send_failed"})
                
                # 🔥 关键修复：添加批次间间隔控制
                if i + batch_size < len(symbols):  # 不是最后一个批次
                    await asyncio.sleep(self.config["batch_interval"])
                    
            except Exception as e:
                self.logger.error(f"OKX批量订阅批次{batch_num}失败: {e}")
                self.log_subscription_attempt(market_type, batch_symbols, False,
                                            {"batch_num": batch_num, "error": str(e)})
                
        # 记录整体订阅结果
        total_batches = (len(symbols) + batch_size - 1) // batch_size
        self.log_subscription_attempt(market_type, symbols, success_count > 0,
                                    {"success_symbols": success_count, "total_batches": total_batches})
        
        self.logger.info(f"✅ OKX修复批量订阅完成: {success_count}/{len(symbols)} 成功")
        return success_count > 0


class BybitWebSocketFixer(WebSocketClientFixer):
    """Bybit WebSocket修复器"""
    
    def __init__(self):
        super().__init__("bybit")
        
    async def fixed_batch_subscribe_with_wait_restored(self, client, symbols: List[str], 
                                                     market_type: str) -> bool:
        """修复后的Bybit订阅方法 - 恢复等待时间"""
        success_count = 0
        batch_size = self.config["batch_size"]  # 统一批次大小
        
        self.logger.info(f"🔧 Bybit修复订阅开始: {len(symbols)}个交易对，批次大小{batch_size}")
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]
            batch_num = i//batch_size + 1
            
            try:
                # 构建Bybit订阅消息
                channels = [f"orderbook.50.{symbol}" for symbol in batch_symbols]
                
                sub_msg = {
                    "op": "subscribe",
                    "args": channels
                }
                
                self.logger.debug(f"🔧 Bybit修复订阅 批次{batch_num}: {len(batch_symbols)}个交易对")
                
                # 发送订阅
                success = await client.send(sub_msg)
                
                if success:
                    success_count += len(batch_symbols) 
                    for symbol in batch_symbols:
                        self.log_data_received(market_type, symbol, {"batch_subscription": "success"})
                else:
                    self.log_subscription_attempt(market_type, batch_symbols, False,
                                                {"batch_num": batch_num, "error": "send_failed"})
                
                # 🔥 关键修复：恢复适当的等待时间，不再是0
                if i + batch_size < len(symbols):  # 不是最后一个批次
                    await asyncio.sleep(self.config["subscription_interval"])
                    
            except Exception as e:
                self.logger.error(f"Bybit订阅批次{batch_num}失败: {e}")
                self.log_subscription_attempt(market_type, batch_symbols, False,
                                            {"batch_num": batch_num, "error": str(e)})
                
        # 记录整体订阅结果
        total_batches = (len(symbols) + batch_size - 1) // batch_size
        self.log_subscription_attempt(market_type, symbols, success_count > 0,
                                    {"success_symbols": success_count, "total_batches": total_batches})
        
        self.logger.info(f"✅ Bybit修复订阅完成: {success_count}/{len(symbols)} 成功")
        return success_count > 0


class UnifiedWebSocketRecoveryManager:
    """统一WebSocket恢复管理器"""
    
    def __init__(self):
        self.fixers = {
            "gate": GateWebSocketFixer(),
            "okx": OKXWebSocketFixer(),
            "bybit": BybitWebSocketFixer()
        }
        self.logger = logging.getLogger("unified_websocket_recovery")
        
        # 🔥 集成阻塞追踪器
        try:
            from websocket.enhanced_blocking_tracker import get_blocking_tracker
            self.blocking_tracker = get_blocking_tracker()
        except ImportError:
            self.blocking_tracker = None
            
    async def apply_unified_fixes(self, exchange: str, client, symbols: List[str], 
                                market_type: str) -> bool:
        """应用统一修复策略"""
        if exchange not in self.fixers:
            self.logger.error(f"不支持的交易所: {exchange}")
            return False
            
        fixer = self.fixers[exchange]
        
        self.logger.info(f"🔧 开始应用{exchange}统一修复策略")
        
        try:
            if exchange == "gate":
                success = await fixer.fixed_subscribe_with_rate_control(
                    client, symbols, market_type
                )
            elif exchange == "okx":
                success = await fixer.fixed_batch_subscribe_with_rate_control(
                    client, symbols, market_type
                )
            elif exchange == "bybit":
                success = await fixer.fixed_batch_subscribe_with_wait_restored(
                    client, symbols, market_type
                )
            else:
                success = False
                
            if success:
                self.logger.info(f"✅ {exchange}统一修复策略应用成功")
            else:
                self.logger.warning(f"⚠️ {exchange}统一修复策略应用部分失败")
                
            return success
            
        except Exception as e:
            self.logger.error(f"❌ {exchange}统一修复策略应用失败: {e}")
            if self.blocking_tracker:
                self.blocking_tracker.log_connection_event(
                    exchange, market_type, "fix_application_failed", {"error": str(e)}
                )
            return False
            
    def get_unified_connection_config(self, exchange: str) -> Dict[str, Any]:
        """获取统一的连接配置"""
        base_config = UnifiedWebSocketFixStrategy.get_optimized_subscription_config()
        
        # 交易所特定的URL配置
        exchange_urls = {
            "gate": {
                "spot": "wss://api.gateio.ws/ws/v4/",
                "futures": "wss://fx-ws.gateio.ws/v4/ws/usdt"
            },
            "okx": {
                "spot": "wss://ws.okx.com:8443/ws/v5/public",
                "futures": "wss://ws.okx.com:8443/ws/v5/public"
            },
            "bybit": {
                "spot": "wss://stream.bybit.com/v5/public/spot",
                "futures": "wss://stream.bybit.com/v5/public/linear"
            }
        }
        
        return {
            **base_config,
            "urls": exchange_urls.get(exchange, {}),
            "exchange": exchange
        }
        
    async def monitor_and_recover(self, exchange: str, client, symbols: List[str], 
                                market_type: str, monitoring_duration: int = 300):
        """监控并自动恢复数据流"""
        self.logger.info(f"🔍 开始监控{exchange}数据流，持续{monitoring_duration}秒")
        
        start_time = time.time()
        last_check = start_time
        recovery_count = 0
        
        while time.time() - start_time < monitoring_duration:
            await asyncio.sleep(30)  # 每30秒检查一次
            
            current_time = time.time()
            
            # 检查是否需要恢复
            if self.blocking_tracker:
                summary = self.blocking_tracker.get_blocking_summary()
                exchange_status = summary["exchanges_status"]
                
                for key, status in exchange_status.items():
                    if key.startswith(exchange) and status["status"] == "blocked":
                        self.logger.warning(f"🚨 检测到{key}数据流阻塞，尝试恢复...")
                        
                        # 尝试重新订阅
                        recovery_success = await self.apply_unified_fixes(
                            exchange, client, symbols, market_type
                        )
                        
                        if recovery_success:
                            recovery_count += 1
                            self.logger.info(f"✅ {key}数据流恢复成功 (第{recovery_count}次)")
                        else:
                            self.logger.error(f"❌ {key}数据流恢复失败")
                            
            last_check = current_time
            
        self.logger.info(f"🔍 {exchange}数据流监控结束，共执行{recovery_count}次恢复")


# 🔥 全局单例实例
_recovery_manager_instance = None

def get_unified_recovery_manager() -> UnifiedWebSocketRecoveryManager:
    """获取统一WebSocket恢复管理器单例"""
    global _recovery_manager_instance
    if _recovery_manager_instance is None:
        _recovery_manager_instance = UnifiedWebSocketRecoveryManager()
    return _recovery_manager_instance


if __name__ == "__main__":
    # 测试统一修复策略
    print("🧪 统一WebSocket修复策略测试")
    
    # 显示优化后的配置
    config = UnifiedWebSocketFixStrategy.get_optimized_subscription_config()
    print("🔧 统一配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
        
    print("\n✅ 统一修复策略已就绪")