
2025-08-03 14:48:59.296 [ERROR] [websocket] [GATE] ❌ [GATE-FUTURES] 订阅失败: {'time': 1754225339, 'time_ms': 1754225339217, 'conn_id': '93d1647db6e1ecf9', 'trace_id': 'bcb98a5cb8794909ef3e30fb02fcebce', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}
2025-08-03 14:48:59.296 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754225339, 'time_ms': 1754225339217, 'conn_id': '93d1647db6e1ecf9', 'trace_id': 'bcb98a5cb8794909ef3e30fb02fcebce', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 14:49:03.461 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:49:04.073 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:49:04.074 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:49:04.310 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754225344, 'time_ms': 1754225344244, 'conn_id': '5bbdcb6d37237392', 'trace_id': '98bf75ab09bd1b900b0c29572e98317e', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '98bf75ab09bd1b900b0c29572e98317e'}
2025-08-03 14:49:04.310 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754225344, 'time_ms': 1754225344244, 'conn_id': '5bbdcb6d37237392', 'trace_id': '98bf75ab09bd1b900b0c29572e98317e', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '98bf75ab09bd1b900b0c29572e98317e'}"}
2025-08-03 14:49:27.244 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:49:27.293 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754225367, 'time_ms': 1754225367169, 'conn_id': 'b1109eebf64d840d', 'trace_id': '370669666903822a37b7f622d35aa13d', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '370669666903822a37b7f622d35aa13d'}
2025-08-03 14:49:27.293 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754225367, 'time_ms': 1754225367169, 'conn_id': 'b1109eebf64d840d', 'trace_id': '370669666903822a37b7f622d35aa13d', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '370669666903822a37b7f622d35aa13d'}"}
2025-08-03 14:49:27.298 [ERROR] [websocket] [GATE] ❌ [GATE-FUTURES] 订阅失败: {'time': 1754225367, 'time_ms': 1754225367170, 'conn_id': '38d477208af17304', 'trace_id': '58a36a193fb422f4f3143af8dc93e2db', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}
2025-08-03 14:49:27.298 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754225367, 'time_ms': 1754225367170, 'conn_id': '38d477208af17304', 'trace_id': '58a36a193fb422f4f3143af8dc93e2db', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 14:49:27.401 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:50:21.169 [ERROR] [asyncio] Future exception was never retrieved
future: <Future finished exception=ConnectionClosedError(None, Close(code=1000, reason=''), None)>
websockets.exceptions.ConnectionClosedError: sent 1000 (OK); no close frame received
2025-08-03 14:50:34.541 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.557 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):



InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 14:50:38.242 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503


2025-08-03 14:50:38,242 [RECOVERY] WARNING - [OKX] [connection_error] 开始处理错误 | {'error_message': 'server rejected WebSocket connection: HTTP 503', 'error_code': ''}



2025-08-03 14:48:59,296 [SUB_FAIL] ERROR - [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754225339, 'time_ms': 1754225339217, 'conn_id': '93d1647db6e1ecf9', 'trace_id': 'bcb98a5cb8794909ef3e30fb02fcebce', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 14:49:04,310 [SUB_FAIL] ERROR - [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754225344, 'time_ms': 1754225344244, 'conn_id': '5bbdcb6d37237392', 'trace_id': '98bf75ab09bd1b900b0c29572e98317e', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '98bf75ab09bd1b900b0c29572e98317e'}"}
2025-08-03 14:49:27,293 [SUB_FAIL] ERROR - [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754225367, 'time_ms': 1754225367169, 'conn_id': 'b1109eebf64d840d', 'trace_id': '370669666903822a37b7f622d35aa13d', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '370669666903822a37b7f622d35aa13d'}"}
2025-08-03 14:49:27,298 [SUB_FAIL] ERROR - [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754225367, 'time_ms': 1754225367170, 'conn_id': '38d477208af17304', 'trace_id': '58a36a193fb422f4f3143af8dc93e2db', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}





2025-08-03 14:50:31,979 [SILENT] WARNING - [okx] 检测到数据流阻塞 | {'silent_duration_seconds': 42.077, 'last_update_time': 1754225389902}
2025-08-03 14:50:32,000 [SILENT] WARNING - [gate] 检测到数据流阻塞 | {'silent_duration_seconds': 36.648, 'last_update_time': 1754225395351}




2025-08-03 14:50:10,829 [PERF] DEBUG - 数据新鲜度检查失败，丢弃过期时间戳 | {'exchange': 'okx', 'timestamp_age_ms': 26422, 'max_age_ms': 1000, 'extraction_source': 'okx_ts_field', 'discarded_timestamp': 1754225384407}
2025-08-03 14:50:10,850 [PERF] DEBUG - 数据新鲜度检查失败，丢弃过期时间戳 | {'exchange': 'gate', 'timestamp_age_ms': 22284, 'max_age_ms': 1000, 'extraction_source': 'gate_t_field', 'discarded_timestamp': 1754225388566}
