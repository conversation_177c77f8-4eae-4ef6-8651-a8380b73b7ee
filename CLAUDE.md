# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-exchange cryptocurrency arbitrage system (期现套利自动化系统) that performs futures premium arbitrage across Gate.io, Bybit, and OKX exchanges. The system monitors price differences between spot and futures markets, executing trades to lock in profit spreads.

**Core Arbitrage Logic:**
- Monitor futures premium spreads (+X% when futures > spot price)  
- Open positions when futures premium threshold reached (buy spot + sell futures)
- Wait for price convergence 
- Close positions when spot premium threshold reached (sell spot + buy futures)
- Total profit = |futures premium| + |spot premium|

## Development Commands

### Running the System
```bash
# Main entry point
python main.py

# Run from 123/ subdirectory (main working directory)
cd 123
python main.py
```

### Dependencies
```bash
# Install dependencies
pip install -r requirements.txt

# Key dependencies: aiohttp, websockets, pandas, gate-api, pybit, ccxt
```

### Testing
```bash
# Run comprehensive validation tests
python tests/comprehensive_fix_validation.py
python tests/institutional_fix_verification.py

# Run specific diagnostics
python diagnostic_scripts/trading_rules_diagnosis.py
python diagnostic_scripts/execution_delay_diagnosis.py
```

### Configuration
- Copy `.env.sample` to `.env` and configure API keys
- Main config in `config/settings.py`
- Environment variables loaded automatically by main.py

## Architecture

### Core Modules (in 123/core/)
- **ArbitrageEngine** - Main orchestrator and control flow
- **OpportunityScanner** - Real-time spread monitoring and detection
- **ExecutionEngine** - Order placement and execution coordination
- **ConvergenceMonitor** - Price spread convergence tracking
- **UnifiedOrderSpreadCalculator** - Unified spread calculation across exchanges

### Exchange Integration (in 123/exchanges/)
- **exchanges_base.py** - Base exchange interface
- **gate_exchange.py** - Gate.io implementation  
- **bybit_exchange.py** - Bybit implementation
- **okx_exchange.py** - OKX implementation
- **unified_exchange_initializer.py** - Exchange factory

### WebSocket System (in 123/websocket/)
- **unified_websocket_pool_manager.py** - Connection pool management
- **ws_manager.py** - WebSocket lifecycle management
- **{exchange}_ws.py** - Exchange-specific WebSocket implementations
- **unified_data_formatter.py** - Standardized data formatting

### Key Features
- 30-level orderbook depth support with binary search algorithms
- Sub-30ms execution latency from detection to lock-in
- 5-tier caching system for zero-latency validation
- 98% hedge quality with intelligent coordination
- Real-time logging of all spreads regardless of thresholds

### Data Flow
1. WebSocket feeds → Unified data formatting → Spread calculation
2. Opportunity scanning → Threshold validation → Execution preparation  
3. Coordinated order placement → Position monitoring → Convergence detection
4. Close signal detection → Position closing → Profit realization

## Important Notes

- The main working directory is `123/` - most development should happen there
- System supports any cryptocurrency pair, not limited to specific tokens
- All price spreads are logged in real-time; thresholds only control execution
- +/- symbols indicate spread type (futures vs spot premium), not positive/negative values
- Configuration uses environment variables loaded via python-dotenv
- Extensive diagnostic and monitoring capabilities built-in

## File Structure Context
- `123/` - Main application code
- `docs/` - Comprehensive documentation (Chinese)
- `diagnostic_scripts/` & `diagnostic_results/` - System analysis tools
- `官方SDK/` - Official exchange SDK packages
- Root level contains additional diagnostic tools and analysis