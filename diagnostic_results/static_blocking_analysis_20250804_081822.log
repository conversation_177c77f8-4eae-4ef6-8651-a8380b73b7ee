2025-08-04 08:18:22,509 - INFO - 🔍 静态数据流阻塞分析器初始化完成
2025-08-04 08:18:22,509 - INFO - 🚀 开始静态数据流阻塞分析
2025-08-04 08:18:22,509 - INFO - 🔍 分析WebSocket代码中的潜在阻塞原因...
2025-08-04 08:18:22,509 - WARNING - 🚨 代码问题 [gate]: 订阅间隔过短 (0.02秒) (严重度: high)
2025-08-04 08:18:22,509 - WARNING - 🚨 代码问题 [gate]: 订阅参数可能不正确 (严重度: medium)
2025-08-04 08:18:22,509 - WARNING - 🚨 代码问题 [okx]: 批量订阅缺乏速率控制 (严重度: high)
2025-08-04 08:18:22,509 - WARNING - 🚨 代码问题 [bybit]: 删除了订阅等待时间 (严重度: medium)
2025-08-04 08:18:22,509 - WARNING - 🚨 代码问题 [all]: 缺乏数据流阻塞检测日志 (严重度: high)
2025-08-04 08:18:22,509 - INFO - 🔍 分析配置相关的阻塞问题...
2025-08-04 08:18:22,509 - WARNING - ⚙️ 配置问题: 三个交易所订阅频率不一致
2025-08-04 08:18:22,510 - WARNING - ⚙️ 配置问题: 过多WebSocket连接
2025-08-04 08:18:22,510 - WARNING - ⚙️ 配置问题: 0.3秒扫描间隔可能过高
2025-08-04 08:18:22,510 - WARNING - ⚙️ 配置问题: 缺乏统一的重连策略
2025-08-04 08:18:22,510 - INFO - 🔍 分析潜在的API使用违规...
2025-08-04 08:18:22,510 - WARNING - ⚠️ API违规风险 [gate]: 订阅频率可能超限 (风险: high)
2025-08-04 08:18:22,510 - WARNING - ⚠️ API违规风险 [okx]: 批量订阅可能超出单次限制 (风险: medium)
2025-08-04 08:18:22,510 - WARNING - ⚠️ API违规风险 [bybit]: args size可能超限 (风险: low)
2025-08-04 08:18:22,510 - WARNING - ⚠️ API违规风险 [all]: WebSocket连接数可能过多 (风险: high)
2025-08-04 08:18:22,510 - INFO - 🔍 分析现有日志文件中的阻塞线索...
2025-08-04 08:18:24,565 - INFO - 📄 分析了17个日志文件
2025-08-04 08:18:24,565 - INFO - 📝 生成数据流阻塞分析报告...
2025-08-04 08:18:24,566 - INFO - 📄 分析报告已保存: diagnostic_results/static_blocking_analysis_report_20250804_081824.json
2025-08-04 08:18:24,566 - INFO - ✅ 静态数据流阻塞分析完成
