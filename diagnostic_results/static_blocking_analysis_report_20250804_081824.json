{"code_issues": [{"exchange": "gate", "file": "gate_ws.py", "line": 209, "issue": "订阅间隔过短 (0.02秒)", "severity": "high", "description": "await asyncio.sleep(0.02) 可能导致频率过高触发限速", "recommendation": "增加到0.1-0.2秒间隔"}, {"exchange": "gate", "file": "gate_ws.py", "lines": "182-190", "issue": "订阅参数可能不正确", "severity": "medium", "description": "现货使用频率参数[symbol, \"10\", \"100ms\"]，期货只用[symbol]", "recommendation": "统一订阅参数格式，参考官方文档"}, {"exchange": "okx", "file": "okx_ws.py", "lines": "177-198", "issue": "批量订阅缺乏速率控制", "severity": "high", "description": "批量订阅10个频道无间隔控制", "recommendation": "添加批次间隔和速率限制"}, {"exchange": "bybit", "file": "bybit_ws.py", "line": 710, "issue": "删除了订阅等待时间", "severity": "medium", "description": "注释掉await asyncio.sleep(0.02)可能过于激进", "recommendation": "恢复适当的订阅间隔"}, {"exchange": "all", "files": ["gate_ws.py", "okx_ws.py", "bybit_ws.py"], "issue": "缺乏数据流阻塞检测日志", "severity": "high", "description": "系统有last_data_time监控但缺乏详细的阻塞原因日志", "recommendation": "添加详细的阻塞检测和原因分析日志"}], "configuration_problems": [{"category": "订阅频率配置", "issue": "三个交易所订阅频率不一致", "description": "Gate: 0.02s, OKX: 批量无间隔, Bybit: 无等待", "impact": "可能导致不同交易所的限速表现不同", "recommendation": "统一订阅频率为0.1-0.2秒"}, {"category": "连接数配置", "issue": "过多WebSocket连接", "description": "7个交易对 × 3个交易所 × 2个市场类型 = 可能42个连接", "impact": "超出交易所连接限制", "recommendation": "优化连接复用，减少总连接数"}, {"category": "扫描频率配置", "issue": "0.3秒扫描间隔可能过高", "description": "高频扫描 + 高频订阅可能触发综合限速", "impact": "系统整体频率过高", "recommendation": "优化扫描频率或订阅策略"}, {"category": "重连配置", "issue": "缺乏统一的重连策略", "description": "三个交易所的重连机制不一致", "impact": "阻塞后恢复能力不同", "recommendation": "实施统一的指数退避重连策略"}], "api_violations": [{"exchange": "gate", "violation": "订阅频率可能超限", "api_limit": "未明确，但0.02秒间隔可能过高", "current_usage": "0.02秒间隔批量订阅", "risk_level": "high"}, {"exchange": "okx", "violation": "批量订阅可能超出单次限制", "api_limit": "官方建议单次订阅不超过特定数量", "current_usage": "10个频道批量订阅", "risk_level": "medium"}, {"exchange": "bybit", "violation": "args size可能超限", "api_limit": "错误信息显示\"args size >10\"", "current_usage": "批次大小8个，应该在限制内", "risk_level": "low"}, {"exchange": "all", "violation": "WebSocket连接数可能过多", "api_limit": "多数交易所限制单IP连接数", "current_usage": "多个市场类型 × 多个交易对", "risk_level": "high"}], "blocking_causes": [{"cause": "高频订阅触发限速", "confidence": "high", "evidence": ["gate: 订阅间隔过短 (0.02秒)", "okx: 批量订阅缺乏速率控制", "all: 缺乏数据流阻塞检测日志"], "impact": "导致WebSocket连接被交易所限制或断开"}, {"cause": "系统配置不当导致资源过载", "confidence": "medium", "evidence": ["连接数过多", "扫描频率过高", "重连策略不一致"], "impact": "系统整体性能下降，数据流不稳定"}, {"cause": "违反交易所API使用规范", "confidence": "high", "evidence": ["gate: 订阅频率可能超限", "all: WebSocket连接数可能过多"], "impact": "触发交易所保护机制，连接被限制"}], "recommendations": [{"priority": "high", "action": "统一订阅频率控制", "details": "将三个交易所的订阅间隔统一为0.1-0.2秒，避免高频触发限速"}, {"priority": "high", "action": "实施连接池优化", "details": "减少WebSocket连接数，使用连接复用技术"}, {"priority": "medium", "action": "增强数据流监控", "details": "添加详细的数据流阻塞检测日志，精确定位阻塞时间点和原因"}, {"priority": "medium", "action": "优化错误处理机制", "details": "改进智能错误过滤，确保重要错误不被隐藏"}, {"priority": "low", "action": "调整系统扫描频率", "details": "考虑将0.3秒扫描间隔调整为0.5秒，减少系统整体负载"}], "log_analysis": {"files_found": 17, "error_patterns": [{"file": "123/logs/websocket_subscription_failure_20250803.log", "keyword": "error", "count": 24}, {"file": "123/logs/bybit_exchange.log", "keyword": "error", "count": 22}, {"file": "123/logs/OKXExchange.log", "keyword": "error", "count": 16}, {"file": "123/logs/error_20250803.log", "keyword": "error", "count": 177}, {"file": "123/logs/error_20250803.log", "keyword": "traceback", "count": 26}], "blocking_indicators": [{"file": "123/logs/websocket_prices.log", "keyword": "429", "context": "Found in log file"}, {"file": "123/logs/OKXExchange.log", "keyword": "too many", "context": "Found in log file"}, {"file": "123/logs/websocket_silent_disconnect_20250803.log", "keyword": "阻塞", "context": "Found in log file"}, {"file": "123/logs/websocket_performance_20250803.log", "keyword": "429", "context": "Found in log file"}, {"file": "123/logs/error_20250803.log", "keyword": "timeout", "context": "Found in log file"}, {"file": "123/logs/error_20250803.log", "keyword": "too many", "context": "Found in log file"}, {"file": "diagnostic_results/static_blocking_analysis_20250804_081822.log", "keyword": "阻塞", "context": "Found in log file"}, {"file": "diagnostic_results/websocket_blocking_diagnosis_20250804_081503.log", "keyword": "阻塞", "context": "Found in log file"}, {"file": "diagnostic_results/websocket_blocking_diagnosis_20250804_081503.log", "keyword": "blocking", "context": "Found in log file"}, {"file": "diagnostic_results/websocket_blocking_diagnosis_20250804_081603.log", "keyword": "阻塞", "context": "Found in log file"}, {"file": "diagnostic_results/websocket_blocking_diagnosis_20250804_081603.log", "keyword": "blocking", "context": "Found in log file"}], "rate_limit_evidence": []}}