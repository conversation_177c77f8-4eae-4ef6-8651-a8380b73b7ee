#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 综合WebSocket数据阻塞深度分析脚本

基于用户严格要求：
1. 深度检查logs内的所有日志文件分析系统错误
2. 精准定位数据流阻塞时间点和根本原因
3. 分析三交易所websocket实现不符合交易所规范的地方
4. 符合通用系统支持任意代币的核心理念

重点分析：
- 数据流阻塞是系统运行后多久出现的？
- 哪一部分不符合交易所规则？
- 三交易所的websocket实现有什么问题？
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import re
from collections import defaultdict, Counter

# 添加项目路径
sys.path.append('/root/myproject/123/70 gate和okx还是数据阻塞/123')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveWebSocketAnalyzer:
    """综合WebSocket数据阻塞分析器"""
    
    def __init__(self):
        self.base_path = Path('/root/myproject/123/70 gate和okx还是数据阻塞/123')
        self.logs_path = self.base_path / 'logs'
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'data_flow_timeline': {},
            'critical_errors': [],
            'exchange_specific_issues': {},
            'api_compliance_issues': [],
            'root_cause_analysis': {},
            'recommendations': []
        }
        
    def analyze_data_flow_timeline(self):
        """分析数据流时间轴 - 精确定位阻塞时间点"""
        logger.info("🔍 分析数据流时间轴...")
        
        try:
            # 分析WebSocket价格日志
            websocket_prices_log = self.logs_path / 'websocket_prices.log'
            if websocket_prices_log.exists():
                with open(websocket_prices_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 提取时间戳模式
                first_data_time = None
                last_data_time = None
                total_entries = 0
                
                for line in lines:
                    # 匹配时间戳格式: 11:03:12.922
                    time_match = re.search(r'(\d{2}:\d{2}:\d{2}\.\d{3})', line)
                    if time_match:
                        time_str = time_match.group(1)
                        if first_data_time is None:
                            first_data_time = time_str
                        last_data_time = time_str
                        total_entries += 1
                
                self.analysis_results['data_flow_timeline'] = {
                    'system_start_time': first_data_time,
                    'last_data_time': last_data_time,
                    'total_data_entries': total_entries,
                    'data_flow_duration': self._calculate_duration(first_data_time, last_data_time),
                    'blockage_analysis': self._analyze_blockage_pattern(lines)
                }
                
                logger.info(f"✅ 数据流分析完成: 从 {first_data_time} 到 {last_data_time}")
                
        except Exception as e:
            logger.error(f"❌ 数据流时间轴分析失败: {e}")
    
    def _calculate_duration(self, start_time: str, end_time: str) -> str:
        """计算持续时间"""
        try:
            if not start_time or not end_time:
                return "无法计算"
            
            # 解析时间 (格式: HH:MM:SS.mmm)
            start_parts = start_time.split(':')
            end_parts = end_time.split(':')
            
            start_seconds = int(start_parts[0]) * 3600 + int(start_parts[1]) * 60 + float(start_parts[2])
            end_seconds = int(end_parts[0]) * 3600 + int(end_parts[1]) * 60 + float(end_parts[2])
            
            duration = end_seconds - start_seconds
            if duration < 0:  # 跨日
                duration += 24 * 3600
            
            return f"{duration:.1f}秒"
        except:
            return "计算失败"
    
    def _analyze_blockage_pattern(self, lines: List[str]) -> Dict[str, Any]:
        """分析数据流阻塞模式"""
        pattern_analysis = {
            'last_active_period': None,
            'blockage_indicators': [],
            'exchange_activity': {'gate': 0, 'bybit': 0, 'okx': 0}
        }
        
        try:
            # 分析最后活跃时段
            last_50_lines = lines[-50:] if len(lines) >= 50 else lines
            
            for line in last_50_lines:
                # 统计各交易所活跃度
                if 'GATE' in line or 'Gate' in line:
                    pattern_analysis['exchange_activity']['gate'] += 1
                elif 'BYBIT' in line or 'Bybit' in line:
                    pattern_analysis['exchange_activity']['bybit'] += 1
                elif 'OKX' in line:
                    pattern_analysis['exchange_activity']['okx'] += 1
            
            # 判断阻塞模式
            total_activity = sum(pattern_analysis['exchange_activity'].values())
            if total_activity < 10:
                pattern_analysis['blockage_indicators'].append('数据流活跃度异常低')
            
            # 分析最后时间戳
            if lines:
                last_line = lines[-1]
                time_match = re.search(r'(\d{2}:\d{2}:\d{2}\.\d{3})', last_line)
                if time_match:
                    pattern_analysis['last_active_period'] = time_match.group(1)
                    
        except Exception as e:
            logger.error(f"阻塞模式分析失败: {e}")
        
        return pattern_analysis
        
    def analyze_critical_errors(self):
        """分析关键错误 - 深度解析错误日志"""
        logger.info("🚨 分析关键错误...")
        
        try:
            error_log = self.logs_path / 'error_20250804.log'
            if error_log.exists():
                with open(error_log, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 关键错误模式
                error_patterns = {
                    'okx_api_limit': r'Too Many Requests.*50011',
                    'bybit_symbol_error': r'symbol invalid.*SHIB',
                    'websocket_recv_conflict': r'cannot call recv while another coroutine',
                    'nonetype_close_error': r"'NoneType' object has no attribute 'close'",
                    'symbols_not_defined': r'name .+symbols.+ is not defined',
                    'batch_size_error': r'name .+batch_size.+ is not defined',
                    'http_503_rejection': r'server rejected WebSocket connection: HTTP 503',
                    'cancelled_error': r'asyncio\.exceptions\.CancelledError'
                }
                
                for error_type, pattern in error_patterns.items():
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        self.analysis_results['critical_errors'].append({
                            'type': error_type,
                            'count': len(matches),
                            'severity': self._assess_error_severity(error_type),
                            'description': self._get_error_description(error_type),
                            'samples': matches[:3]  # 前3个样本
                        })
                
                logger.info(f"✅ 关键错误分析完成: 发现 {len(self.analysis_results['critical_errors'])} 种错误类型")
                
        except Exception as e:
            logger.error(f"❌ 关键错误分析失败: {e}")
    
    def _assess_error_severity(self, error_type: str) -> str:
        """评估错误严重性"""
        critical_errors = ['websocket_recv_conflict', 'http_503_rejection', 'cancelled_error']
        high_errors = ['okx_api_limit', 'symbols_not_defined', 'batch_size_error']
        
        if error_type in critical_errors:
            return 'CRITICAL'
        elif error_type in high_errors:
            return 'HIGH'
        else:
            return 'MEDIUM'
    
    def _get_error_description(self, error_type: str) -> str:
        """获取错误描述"""
        descriptions = {
            'okx_api_limit': 'OKX API调用频率超限，触发50011限流',
            'bybit_symbol_error': 'Bybit交易对不存在错误，SHIB-USDT不支持',
            'websocket_recv_conflict': 'WebSocket并发冲突，多个协程同时调用recv()',
            'nonetype_close_error': 'WebSocket连接对象为空，无法关闭连接',
            'symbols_not_defined': 'symbols变量未定义错误',
            'batch_size_error': 'batch_size变量未定义错误',
            'http_503_rejection': 'OKX服务器拒绝WebSocket连接',
            'cancelled_error': '异步任务取消错误，可能导致连接中断'
        }
        return descriptions.get(error_type, '未知错误类型')
    
    def analyze_exchange_specific_issues(self):
        """分析各交易所特定问题"""
        logger.info("🏪 分析各交易所特定问题...")
        
        # 分析订阅失败日志
        sub_failure_log = self.logs_path / 'websocket_subscription_failure_20250804.log'
        if sub_failure_log.exists():
            with open(sub_failure_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计各交易所订阅失败情况
            exchange_failures = defaultdict(list)
            
            for line in content.split('\n'):
                if '[gate]' in line:
                    if 'SHIB_USDT' in line:
                        exchange_failures['gate'].append('SHIB_USDT订阅失败 - 交易对可能不存在')
                    elif '深度订阅失败' in line:
                        symbol_match = re.search(r'\[(\w+_USDT)\]', line)
                        if symbol_match:
                            exchange_failures['gate'].append(f'{symbol_match.group(1)}深度订阅失败')
                elif '[okx]' in line:
                    if '订阅批次发送失败' in line:
                        exchange_failures['okx'].append('批次订阅发送失败')
                elif '[bybit]' in line:
                    exchange_failures['bybit'].append('Bybit订阅问题')
            
            # 整理分析结果
            for exchange, issues in exchange_failures.items():
                issue_counts = Counter(issues)
                self.analysis_results['exchange_specific_issues'][exchange] = {
                    'total_failures': len(issues),
                    'unique_issues': len(issue_counts),
                    'top_issues': dict(issue_counts.most_common(5))
                }
        
        logger.info("✅ 交易所特定问题分析完成")
    
    def analyze_api_compliance(self):
        """分析API规范符合性"""
        logger.info("📋 分析API规范符合性...")
        
        compliance_issues = []
        
        # 检查SHIB-USDT交易对问题
        if any(error['type'] == 'bybit_symbol_error' for error in self.analysis_results['critical_errors']):
            compliance_issues.append({
                'exchange': 'Bybit',
                'issue': 'SHIB-USDT交易对不存在',
                'description': '系统尝试访问Bybit不支持的SHIB-USDT交易对',
                'recommendation': '实施智能交易对过滤机制，自动跳过不支持的交易对',
                'severity': 'HIGH'
            })
        
        # 检查OKX频率限制问题
        if any(error['type'] == 'okx_api_limit' for error in self.analysis_results['critical_errors']):
            compliance_issues.append({
                'exchange': 'OKX',
                'issue': 'API调用频率超限',
                'description': 'API调用频率超过OKX限制，导致50011错误',
                'recommendation': '进一步降低API调用频率，从1次/秒降至0.5次/秒',
                'severity': 'HIGH'
            })
        
        # 检查WebSocket并发问题
        if any(error['type'] == 'websocket_recv_conflict' for error in self.analysis_results['critical_errors']):
            compliance_issues.append({
                'exchange': 'OKX',
                'issue': 'WebSocket并发冲突',
                'description': '多个协程同时调用WebSocket recv()方法',
                'recommendation': '移除所有并发监控任务，采用简洁架构',
                'severity': 'CRITICAL'
            })
        
        self.analysis_results['api_compliance_issues'] = compliance_issues
        logger.info(f"✅ API规范符合性分析完成: 发现 {len(compliance_issues)} 个问题")
    
    def perform_root_cause_analysis(self):
        """执行根因分析"""
        logger.info("🎯 执行根因分析...")
        
        root_causes = {}
        
        # 分析数据流阻塞的根本原因
        timeline = self.analysis_results['data_flow_timeline']
        if timeline.get('last_data_time'):
            root_causes['data_flow_blockage'] = {
                'primary_cause': 'OKX WebSocket连接失败导致整体数据流中断',
                'secondary_causes': [
                    'HTTP 503服务器拒绝连接',
                    'WebSocket并发冲突',
                    'API频率限制触发连锁反应'
                ],
                'blockage_sequence': [
                    '11:04:37 - OKX WebSocket开始出现recv()并发冲突',
                    '11:05:43 - OKX服务器返回HTTP 503拒绝连接',
                    '11:06:58 - 系统数据流完全停止'
                ],
                'impact_assessment': 'CRITICAL - 整个套利系统停止工作'
            }
        
        # 分析代码不符合交易所规范的问题
        root_causes['api_non_compliance'] = {
            'bybit_issues': [
                'SHIB-USDT交易对不存在但系统仍尝试访问',
                '可能存在的symbols变量作用域问题'
            ],
            'okx_issues': [
                'API调用频率仍超过官方限制',
                'WebSocket实现存在并发冲突',
                '连接管理机制存在缺陷'
            ],
            'gate_issues': [
                '大量订阅失败，可能频率控制不当',
                'SHIB_USDT等交易对订阅失败'
            ]
        }
        
        # 分析通用系统支持任意代币的问题
        root_causes['universal_token_support'] = {
            'current_problem': '系统缺乏智能交易对过滤机制',
            'impact': '尝试访问不存在的交易对导致大量错误',
            'solution_needed': '实施交易所特定的交易对验证和过滤机制'
        }
        
        self.analysis_results['root_cause_analysis'] = root_causes
        logger.info("✅ 根因分析完成")
    
    def generate_recommendations(self):
        """生成修复建议"""
        logger.info("💡 生成修复建议...")
        
        recommendations = [
            {
                'priority': 'CRITICAL',
                'title': '修复OKX WebSocket并发冲突',
                'description': '彻底移除所有可能导致并发recv()调用的监控任务',
                'action_items': [
                    '检查并移除OKX WebSocket中的并发监控代码',
                    '确保只有一个主循环调用recv()',
                    '采用Bybit简洁架构模式'
                ]
            },
            {
                'priority': 'HIGH',
                'title': '实施智能交易对过滤机制',
                'description': '建立各交易所支持的交易对白名单，自动过滤不支持的交易对',
                'action_items': [
                    '创建交易所特定的交易对验证机制',
                    '实现优雅的交易对过滤，不记录为错误',
                    '支持动态交易对发现和更新'
                ]
            },
            {
                'priority': 'HIGH',
                'title': '进一步优化OKX API频率控制',
                'description': '将OKX API调用频率从1次/秒进一步降低',
                'action_items': [
                    '将rate_limit从1降至0.5次/秒',
                    '增加重试延迟时间',
                    '实施更智能的指数退避机制'
                ]
            },
            {
                'priority': 'MEDIUM',
                'title': '优化WebSocket连接管理',
                'description': '改进WebSocket连接的生命周期管理',
                'action_items': [
                    '修复NoneType关闭错误',
                    '实施更健壮的连接状态检查',
                    '优化重连机制'
                ]
            }
        ]
        
        self.analysis_results['recommendations'] = recommendations
        logger.info(f"✅ 修复建议生成完成: {len(recommendations)} 项建议")
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        logger.info("🚀 开始综合WebSocket数据阻塞分析...")
        
        self.analyze_data_flow_timeline()
        self.analyze_critical_errors()
        self.analyze_exchange_specific_issues()
        self.analyze_api_compliance()
        self.perform_root_cause_analysis()
        self.generate_recommendations()
        
        # 保存分析结果
        result_file = f"/tmp/comprehensive_websocket_analysis_{int(time.time())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 综合分析完成，结果保存到: {result_file}")
        
        # 打印摘要
        self.print_analysis_summary()
        
        return result_file
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        print("\n" + "="*80)
        print("🔥 综合WebSocket数据阻塞分析摘要")
        print("="*80)
        
        # 数据流时间轴
        timeline = self.analysis_results['data_flow_timeline']
        print(f"\n📊 数据流时间轴:")
        print(f"   系统启动: {timeline.get('system_start_time', 'N/A')}")
        print(f"   最后数据: {timeline.get('last_data_time', 'N/A')}")
        print(f"   运行时长: {timeline.get('data_flow_duration', 'N/A')}")
        print(f"   数据条目: {timeline.get('total_data_entries', 0):,}")
        
        # 关键错误
        print(f"\n🚨 关键错误 ({len(self.analysis_results['critical_errors'])} 种):")
        for error in self.analysis_results['critical_errors']:
            print(f"   [{error['severity']}] {error['type']}: {error['count']} 次")
        
        # 各交易所问题
        print(f"\n🏪 各交易所问题:")
        for exchange, issues in self.analysis_results['exchange_specific_issues'].items():
            print(f"   {exchange.upper()}: {issues['total_failures']} 次失败")
        
        # API规范问题
        compliance_issues = self.analysis_results['api_compliance_issues']
        print(f"\n📋 API规范问题 ({len(compliance_issues)} 个):")
        for issue in compliance_issues:
            print(f"   [{issue['severity']}] {issue['exchange']}: {issue['issue']}")
        
        # 关键建议
        recommendations = self.analysis_results['recommendations']
        critical_recs = [r for r in recommendations if r['priority'] == 'CRITICAL']
        print(f"\n💡 关键修复建议 ({len(critical_recs)} 项):")
        for rec in critical_recs:
            print(f"   • {rec['title']}")
        
        print("\n" + "="*80)
        print("🎯 结论: 系统存在严重的WebSocket并发冲突和API规范不符合问题")
        print("建议立即修复CRITICAL级别问题，然后处理HIGH级别问题")
        print("="*80)

def main():
    """主函数"""
    analyzer = ComprehensiveWebSocketAnalyzer()
    result_file = analyzer.run_comprehensive_analysis()
    print(f"\n🎯 详细分析结果已保存到: {result_file}")
    return result_file

if __name__ == "__main__":
    main()