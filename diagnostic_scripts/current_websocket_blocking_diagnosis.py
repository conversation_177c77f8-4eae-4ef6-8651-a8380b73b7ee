#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当前WebSocket数据阻塞诊断脚本
基于最新修复记录和官方API文档检查Gate和OKX数据阻塞问题
"""

import os
import sys
import json
import time
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# 设置脚本路径
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.insert(0, parent_dir)

class WebSocketBlockingDiagnosis:
    """WebSocket数据阻塞诊断器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.diagnosis_results = {
            "timestamp": datetime.now().isoformat(),
            "issues": [],
            "recommendations": [],
            "api_compliance": {},
            "data_freshness": {}
        }
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger("websocket_diagnosis")
        logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
        
    def check_api_compliance(self):
        """检查API合规性"""
        self.logger.info("🔍 检查API合规性...")
        
        # 检查OKX API配置
        try:
            from config.unified_api_config import get_exchange_api_config
            okx_config = get_exchange_api_config("okx")
            
            # 根据官方文档：3 requests per second (per IP)
            if hasattr(okx_config, 'rate_limit') and okx_config.rate_limit > 3:
                self.diagnosis_results["issues"].append({
                    "severity": "CRITICAL",
                    "component": "OKX API",
                    "issue": f"API频率限制过高: {okx_config.rate_limit} > 3 requests/second",
                    "recommendation": "降低到1-2 requests/second确保合规"
                })
            else:
                self.diagnosis_results["api_compliance"]["okx_rate_limit"] = "✅ 合规"
                
        except Exception as e:
            self.diagnosis_results["issues"].append({
                "severity": "WARNING",
                "component": "OKX配置",
                "issue": f"无法检查OKX API配置: {e}",
                "recommendation": "检查配置文件是否存在"
            })
            
    def check_websocket_heartbeat_config(self):
        """检查WebSocket心跳配置"""
        self.logger.info("💓 检查WebSocket心跳配置...")
        
        # 检查Gate.io心跳配置
        try:
            import websocket.gate_ws as gate_ws
            # 通过代码检查心跳间隔设置
            # 根据修复记录，应该是10秒
            
            # 读取gate_ws.py文件内容
            gate_ws_file = os.path.join(parent_dir, "websocket", "gate_ws.py")
            if os.path.exists(gate_ws_file):
                with open(gate_ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "heartbeat_interval = 5" in content:
                        self.diagnosis_results["issues"].append({
                            "severity": "HIGH",
                            "component": "Gate.io WebSocket",
                            "issue": "心跳间隔仍为5秒，不符合官方建议",
                            "recommendation": "修改为10秒符合规范"
                        })
                    elif "heartbeat_interval = 10" in content:
                        self.diagnosis_results["api_compliance"]["gate_heartbeat"] = "✅ 合规"
                        
        except Exception as e:
            self.diagnosis_results["issues"].append({
                "severity": "WARNING", 
                "component": "Gate.io配置检查",
                "issue": f"无法检查心跳配置: {e}",
                "recommendation": "手动检查websocket/gate_ws.py"
            })
            
    def check_subscription_frequency(self):
        """检查订阅频率设置"""
        self.logger.info("📡 检查订阅频率设置...")
        
        # 检查各个WebSocket文件中的订阅间隔
        exchanges = ["gate_ws.py", "okx_ws.py", "bybit_ws.py"]
        
        for exchange_file in exchanges:
            try:
                file_path = os.path.join(parent_dir, "websocket", exchange_file)
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    exchange_name = exchange_file.replace("_ws.py", "").upper()
                    
                    # 检查订阅间隔
                    if "await asyncio.sleep(0.02)" in content:
                        self.diagnosis_results["issues"].append({
                            "severity": "CRITICAL",
                            "component": f"{exchange_name} WebSocket",
                            "issue": "订阅间隔0.02秒过短，可能导致限流",
                            "recommendation": "修改为0.15秒符合最佳实践"
                        })
                    elif "await asyncio.sleep(0.15)" in content:
                        self.diagnosis_results["api_compliance"][f"{exchange_name.lower()}_subscription"] = "✅ 合规"
                        
            except Exception as e:
                self.logger.warning(f"检查{exchange_file}时出错: {e}")
                
    def check_variable_definitions(self):
        """检查变量定义问题"""
        self.logger.info("🔤 检查变量定义问题...")
        
        # 检查Bybit WebSocket的batch_size变量定义问题
        try:
            bybit_file = os.path.join(parent_dir, "websocket", "bybit_ws.py")
            if os.path.exists(bybit_file):
                with open(bybit_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # 检查第700行左右是否有变量未定义问题
                for i, line in enumerate(lines):
                    if "batch_size" in line.lower() and "BATCH_SIZE" not in line:
                        if i > 695 and i < 705:  # 700行附近
                            self.diagnosis_results["issues"].append({
                                "severity": "CRITICAL",
                                "component": "Bybit WebSocket",
                                "issue": f"第{i+1}行: batch_size变量未定义，应为BATCH_SIZE",
                                "recommendation": "修正变量名为BATCH_SIZE"
                            })
                            
        except Exception as e:
            self.logger.warning(f"检查Bybit变量定义时出错: {e}")
            
    def check_concurrent_conflicts(self):
        """检查并发冲突问题"""
        self.logger.info("🔀 检查WebSocket并发冲突问题...")
        
        # 检查OKX是否存在并发监控任务
        try:
            okx_file = os.path.join(parent_dir, "websocket", "okx_ws.py")
            if os.path.exists(okx_file):
                with open(okx_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if "_monitor_data_flow" in content and "def _monitor_data_flow" in content:
                    self.diagnosis_results["issues"].append({
                        "severity": "CRITICAL",
                        "component": "OKX WebSocket",
                        "issue": "存在_monitor_data_flow方法，可能导致recv()并发冲突",
                        "recommendation": "移除监控任务，使用统一连接池管理"
                    })
                else:
                    self.diagnosis_results["api_compliance"]["okx_concurrent"] = "✅ 无并发冲突"
                    
        except Exception as e:
            self.logger.warning(f"检查OKX并发冲突时出错: {e}")
            
    def check_recent_logs(self):
        """检查最近的日志文件是否有数据阻塞迹象"""
        self.logger.info("📝 检查最近的日志文件...")
        
        log_indicators = {
            "data_blocking": ["数据流阻塞", "silent_duration", "data flow blocking"],
            "api_limit": ["Too Many Requests", "50011", "rate limit"],
            "websocket_errors": ["WebSocket", "connection", "recv_conflict"],
            "timestamp_issues": ["timestamp", "数据新鲜度", "time_diff"]
        }
        
        # 检查日志目录
        log_dirs = ["logs", ".", parent_dir]
        
        for log_dir in log_dirs:
            if os.path.exists(log_dir):
                try:
                    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
                    recent_files = sorted(log_files, key=lambda x: os.path.getmtime(os.path.join(log_dir, x)), reverse=True)[:3]
                    
                    for log_file in recent_files:
                        self._analyze_log_file(os.path.join(log_dir, log_file), log_indicators)
                        
                except Exception as e:
                    self.logger.debug(f"检查日志目录{log_dir}时出错: {e}")
                    
    def _analyze_log_file(self, log_path: str, indicators: Dict[str, List[str]]):
        """分析单个日志文件"""
        try:
            if not os.path.exists(log_path):
                return
                
            # 只读取最近的1000行
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                recent_lines = lines[-1000:] if len(lines) > 1000 else lines
                
            for category, keywords in indicators.items():
                count = sum(1 for line in recent_lines 
                           if any(keyword.lower() in line.lower() for keyword in keywords))
                
                if count > 0:
                    severity = "CRITICAL" if count > 10 else "HIGH" if count > 3 else "MEDIUM"
                    self.diagnosis_results["issues"].append({
                        "severity": severity,
                        "component": f"日志分析 - {os.path.basename(log_path)}",
                        "issue": f"发现{count}条{category}相关错误",
                        "recommendation": f"详细检查{category}相关问题"
                    })
                    
        except Exception as e:
            self.logger.debug(f"分析日志文件{log_path}时出错: {e}")
            
    def generate_recommendations(self):
        """生成修复建议"""
        self.logger.info("💡 生成修复建议...")
        
        critical_issues = [issue for issue in self.diagnosis_results["issues"] 
                          if issue["severity"] == "CRITICAL"]
        
        if critical_issues:
            self.diagnosis_results["recommendations"].extend([
                "🚨 发现CRITICAL级别问题，需要立即修复",
                "📝 按照07B_核心问题修复专项文档中的修复方案执行",
                "🔧 建议按以下优先级修复：",
                "   1. API频率限制合规性修复",
                "   2. WebSocket并发冲突修复", 
                "   3. 变量定义错误修复",
                "   4. 订阅频率优化"
            ])
        else:
            self.diagnosis_results["recommendations"].append(
                "✅ 未发现CRITICAL问题，系统配置基本符合官方API规范"
            )
            
    def run_diagnosis(self) -> Dict[str, Any]:
        """运行完整诊断"""
        self.logger.info("🔍 开始WebSocket数据阻塞诊断...")
        
        try:
            self.check_api_compliance()
            self.check_websocket_heartbeat_config()
            self.check_subscription_frequency()
            self.check_variable_definitions()
            self.check_concurrent_conflicts()
            self.check_recent_logs()
            self.generate_recommendations()
            
            # 计算问题统计
            severity_counts = {}
            for issue in self.diagnosis_results["issues"]:
                severity = issue["severity"]
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
                
            self.diagnosis_results["summary"] = {
                "total_issues": len(self.diagnosis_results["issues"]),
                "severity_breakdown": severity_counts,
                "compliance_checks": len(self.diagnosis_results["api_compliance"]),
                "diagnosis_status": "CRITICAL" if severity_counts.get("CRITICAL", 0) > 0 else 
                                  "HIGH" if severity_counts.get("HIGH", 0) > 0 else "GOOD"
            }
            
        except Exception as e:
            self.logger.error(f"诊断过程中出错: {e}")
            self.diagnosis_results["issues"].append({
                "severity": "ERROR",
                "component": "诊断系统",
                "issue": f"诊断过程异常: {e}",
                "recommendation": "检查诊断脚本和依赖"
            })
            
        return self.diagnosis_results
        
    def save_results(self, filename: Optional[str] = None):
        """保存诊断结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"websocket_blocking_diagnosis_{timestamp}.json"
            
        filepath = os.path.join(script_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"✅ 诊断结果已保存到: {filepath}")
        except Exception as e:
            self.logger.error(f"保存诊断结果失败: {e}")
            
    def print_summary(self):
        """打印诊断摘要"""
        print("\n" + "="*60)
        print("🔍 WebSocket数据阻塞诊断结果")
        print("="*60)
        
        summary = self.diagnosis_results["summary"]
        print(f"📊 诊断状态: {summary['diagnosis_status']}")
        print(f"🔢 发现问题: {summary['total_issues']}个")
        
        if summary["severity_breakdown"]:
            print("📈 问题级别分布:")
            for severity, count in summary["severity_breakdown"].items():
                print(f"   {severity}: {count}个")
                
        print(f"✅ 合规检查: {summary['compliance_checks']}项")
        
        if self.diagnosis_results["issues"]:
            print("\n🚨 主要问题:")
            for i, issue in enumerate(self.diagnosis_results["issues"][:5], 1):
                print(f"{i}. [{issue['severity']}] {issue['component']}: {issue['issue']}")
                
        if self.diagnosis_results["recommendations"]:
            print("\n💡 修复建议:")
            for rec in self.diagnosis_results["recommendations"]:
                print(f"   {rec}")
                
        print("="*60)

def main():
    """主函数"""
    print("🔍 WebSocket数据阻塞诊断工具")
    print("基于最新修复记录和官方API文档进行检查")
    print("-" * 50)
    
    diagnosis = WebSocketBlockingDiagnosis()
    results = diagnosis.run_diagnosis()
    
    diagnosis.print_summary()
    diagnosis.save_results()
    
    return results

if __name__ == "__main__":
    results = main()