#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 机构级修复质量全面审查脚本

按照用户要求进行6点核心审查：
1. 使用了统一模块？
2. 修复优化没有造轮子？
3. 没有引入新的问题？
4. 完美修复？
5. 确保功能实现？
6. 没有重复，没有冗余，没有接口不统一、接口不兼容、链路错误？
"""

import sys
import os
import time
import json
import logging
import asyncio
import importlib
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append('/root/myproject/123/70 gate和okx还是数据阻塞/123')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InstitutionalQualityAuditor:
    """机构级修复质量审查器"""
    
    def __init__(self):
        self.audit_results = {
            'unified_modules_usage': {'score': 0, 'details': [], 'passed': False},
            'no_wheel_reinvention': {'score': 0, 'details': [], 'passed': False},
            'no_new_issues': {'score': 0, 'details': [], 'passed': False},
            'perfect_fix': {'score': 0, 'details': [], 'passed': False},
            'functionality_assured': {'score': 0, 'details': [], 'passed': False},
            'no_redundancy_conflicts': {'score': 0, 'details': [], 'passed': False},
            'overall_quality': {'score': 0, 'passed': False}
        }
        
        self.modified_files = [
            '/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/okx_ws.py',
            '/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/bybit_ws.py', 
            '/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/gate_ws.py'
        ]

    def audit_1_unified_modules_usage(self) -> Dict[str, Any]:
        """审查1：使用了统一模块？"""
        logger.info("🔍 [审查1] 统一模块使用审查...")
        
        details = []
        score = 0
        
        try:
            # 检查统一模块导入
            unified_modules_expected = [
                'unified_data_formatter',
                'unified_timestamp_processor', 
                'currency_adapter',
                'orderbook_validator',
                'performance_monitor'
            ]
            
            modules_found = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                    
                content = Path(file_path).read_text(encoding='utf-8')
                exchange_name = Path(file_path).stem.replace('_ws', '').upper()
                
                file_modules = 0
                for module in unified_modules_expected:
                    if module in content:
                        file_modules += 1
                        details.append(f"✅ {exchange_name}: 使用统一模块 {module}")
                
                modules_found += file_modules
                details.append(f"📊 {exchange_name}: 使用 {file_modules}/{len(unified_modules_expected)} 个统一模块")
            
            # 计算得分 (0-100)
            max_possible = len(self.modified_files) * len(unified_modules_expected)
            score = int((modules_found / max_possible) * 100) if max_possible > 0 else 0
            
            # 检查是否有直接重复实现而非使用统一模块
            redundant_implementations = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                
                # 检查是否有重复的时间戳处理逻辑
                if 'time.time()' in content and 'get_synced_timestamp' not in content:
                    redundant_implementations += 1
                    details.append(f"⚠️ {Path(file_path).stem}: 可能存在重复时间处理实现")
            
            if redundant_implementations > 0:
                score = max(0, score - (redundant_implementations * 20))
                
            passed = score >= 80  # 80分以上算通过
            details.append(f"🎯 统一模块使用得分: {score}/100 ({'通过' if passed else '不通过'})")
            
        except Exception as e:
            details.append(f"❌ 统一模块审查异常: {e}")
            score = 0
            passed = False
        
        return {'score': score, 'details': details, 'passed': passed}

    def audit_2_no_wheel_reinvention(self) -> Dict[str, Any]:
        """审查2：修复优化没有造轮子？"""
        logger.info("🔍 [审查2] 避免重复造轮子审查...")
        
        details = []
        score = 100  # 从满分开始，发现问题扣分
        
        try:
            # 检查是否新增了不必要的功能类或方法
            wheel_reinvention_patterns = [
                ('class.*Formatter', '可能重复实现格式化器'),
                ('class.*Validator', '可能重复实现验证器'),
                ('class.*Processor', '可能重复实现处理器'),
                ('def format_.*data', '可能重复实现格式化方法'),
                ('def validate_.*', '可能重复实现验证方法'),
                ('def sync_time', '可能重复实现时间同步')
            ]
            
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                    
                content = Path(file_path).read_text(encoding='utf-8')
                exchange_name = Path(file_path).stem.replace('_ws', '').upper()
                
                for pattern, description in wheel_reinvention_patterns:
                    import re
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        # 检查是否确实是重复实现
                        if not any(unified in content for unified in ['from websocket.unified', 'from exchanges.currency']):
                            score -= 15
                            details.append(f"⚠️ {exchange_name}: {description} - {len(matches)}个匹配")
            
            # 检查修复是否直接使用现有架构
            architecture_usage = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                
                if 'super().run()' in content:
                    architecture_usage += 1
                    details.append(f"✅ {Path(file_path).stem}: 正确使用基类架构")
            
            if architecture_usage >= len(self.modified_files):
                details.append("✅ 所有修复都基于现有架构，无重复造轮子")
            else:
                score -= 20
                details.append("⚠️ 部分修复可能绕过了现有架构")
            
            passed = score >= 80
            details.append(f"🎯 避免造轮子得分: {score}/100 ({'通过' if passed else '不通过'})")
            
        except Exception as e:
            details.append(f"❌ 造轮子审查异常: {e}")
            score = 0
            passed = False
        
        return {'score': score, 'details': details, 'passed': passed}

    def audit_3_no_new_issues(self) -> Dict[str, Any]:
        """审查3：没有引入新的问题？"""
        logger.info("🔍 [审查3] 新问题引入审查...")
        
        details = []
        score = 100
        
        try:
            # 检查潜在的新问题
            potential_issues = [
                ('await asyncio.sleep(0)', '可能引入不必要的延迟'),
                ('time.sleep(', '同步sleep可能阻塞异步循环'),
                ('print(', 'print语句可能影响生产性能'),
                ('import.*\*', 'import *可能引入命名冲突'),
                ('except:', '裸except可能掩盖重要错误'),
                ('pass\s*#.*todo', 'TODO注释表明功能未完成')
            ]
            
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                    
                content = Path(file_path).read_text(encoding='utf-8')
                exchange_name = Path(file_path).stem.replace('_ws', '').upper()
                
                for pattern, issue_desc in potential_issues:
                    import re
                    matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                    if matches:
                        score -= min(20, len(matches) * 5)  # 每个问题扣5分，最多扣20分
                        details.append(f"⚠️ {exchange_name}: {issue_desc} - {len(matches)}处")
            
            # 检查修复是否可能破坏现有功能
            breaking_changes = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                
                # 检查是否直接修改了关键方法签名
                if 'def __init__(' in content and 'super().__init__' not in content:
                    breaking_changes += 1
                    details.append(f"⚠️ {Path(file_path).stem}: 可能修改了初始化签名")
            
            if breaking_changes == 0:
                details.append("✅ 没有发现可能破坏现有功能的修改")
            else:
                score -= breaking_changes * 25
            
            passed = score >= 80
            details.append(f"🎯 新问题引入审查得分: {score}/100 ({'通过' if passed else '不通过'})")
            
        except Exception as e:
            details.append(f"❌ 新问题审查异常: {e}")
            score = 0
            passed = False
        
        return {'score': score, 'details': details, 'passed': passed}

    def audit_4_perfect_fix(self) -> Dict[str, Any]:
        """审查4：完美修复？"""
        logger.info("🔍 [审查4] 完美修复审查...")
        
        details = []
        score = 0
        
        try:
            # 基于之前的诊断结果检查修复完整性
            critical_fixes_required = {
                'okx_concurrency_fix': False,
                'okx_rate_limit_fix': False,
                'symbols_variable_fix': False,
                'intelligent_filtering': False
            }
            
            # 检查OKX并发冲突修复
            okx_file = Path('/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/okx_ws.py')
            if okx_file.exists():
                content = okx_file.read_text(encoding='utf-8')
                if ('auto_recovery_enabled = False' in content and 
                    '_integrated_with_pool = False' in content and
                    'CRITICAL修复' in content):
                    critical_fixes_required['okx_concurrency_fix'] = True
                    details.append("✅ OKX WebSocket并发冲突修复确认")
                    score += 25
                
                if 'await asyncio.sleep(0.5)' in content and 'CRITICAL修复' in content:
                    critical_fixes_required['okx_rate_limit_fix'] = True
                    details.append("✅ OKX API频率限制修复确认")
                    score += 25
            
            # 检查Bybit symbols变量修复
            bybit_file = Path('/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/bybit_ws.py')
            if bybit_file.exists():
                content = bybit_file.read_text(encoding='utf-8')
                if 'len(self.symbols)' in content and 'CRITICAL修复' in content:
                    critical_fixes_required['symbols_variable_fix'] = True
                    details.append("✅ Bybit symbols变量修复确认")
                    score += 25
            
            # 检查智能过滤机制
            filtering_found = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                if ('智能过滤' in content and '自动过滤不支持的交易对' in content):
                    filtering_found += 1
            
            if filtering_found >= 2:  # 至少2个交易所有智能过滤
                critical_fixes_required['intelligent_filtering'] = True
                details.append("✅ 智能交易对过滤机制确认")
                score += 25
            
            # 统计修复完成度
            fixes_completed = sum(critical_fixes_required.values())
            details.append(f"📊 关键修复完成度: {fixes_completed}/4")
            
            passed = score >= 80 and fixes_completed >= 3
            details.append(f"🎯 完美修复得分: {score}/100 ({'通过' if passed else '不通过'})")
            
        except Exception as e:
            details.append(f"❌ 完美修复审查异常: {e}")
            score = 0
            passed = False
        
        return {'score': score, 'details': details, 'passed': passed}

    def audit_5_functionality_assured(self) -> Dict[str, Any]:
        """审查5：确保功能实现？"""
        logger.info("🔍 [审查5] 功能实现确保审查...")
        
        details = []
        score = 0
        
        try:
            # 检查关键功能是否完整保留
            essential_functions = [
                'async def run',
                'async def subscribe_channels', 
                'async def handle_message',
                'async def send_heartbeat'
            ]
            
            functionality_score = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                    
                content = Path(file_path).read_text(encoding='utf-8')
                exchange_name = Path(file_path).stem.replace('_ws', '').upper()
                
                file_functions = 0
                for func in essential_functions:
                    if func in content:
                        file_functions += 1
                
                functionality_score += file_functions
                details.append(f"📊 {exchange_name}: 保留 {file_functions}/{len(essential_functions)} 个核心方法")
            
            # 计算功能完整性得分
            max_possible = len(self.modified_files) * len(essential_functions)
            score = int((functionality_score / max_possible) * 100) if max_possible > 0 else 0
            
            # 检查是否有功能降级
            degradation_indicators = [
                'pass  # TODO',
                'raise NotImplementedError',
                'return None  # Disabled',
                '# FIXME',
                'return False  # Temporary'
            ]
            
            degradations_found = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                
                for indicator in degradation_indicators:
                    if indicator in content:
                        degradations_found += 1
            
            if degradations_found > 0:
                score = max(0, score - (degradations_found * 15))
                details.append(f"⚠️ 发现 {degradations_found} 处可能的功能降级")
            else:
                details.append("✅ 没有发现功能降级指标")
            
            # 检查核心数据处理逻辑是否完整
            data_processing_elements = [
                '_handle_orderbook',
                'emit.*market_data',
                'format_orderbook_data'
            ]
            
            processing_found = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                
                for element in data_processing_elements:
                    import re
                    if re.search(element, content):
                        processing_found += 1
            
            if processing_found >= len(data_processing_elements):
                details.append("✅ 核心数据处理逻辑完整")
                score = min(100, score + 10)
            
            passed = score >= 85
            details.append(f"🎯 功能实现确保得分: {score}/100 ({'通过' if passed else '不通过'})")
            
        except Exception as e:
            details.append(f"❌ 功能实现审查异常: {e}")
            score = 0
            passed = False
        
        return {'score': score, 'details': details, 'passed': passed}

    def audit_6_no_redundancy_conflicts(self) -> Dict[str, Any]:
        """审查6：没有重复、冗余、接口不统一、接口不兼容、链路错误？"""
        logger.info("🔍 [审查6] 冗余冲突审查...")
        
        details = []
        score = 100  # 从满分开始扣分
        
        try:
            # 检查接口一致性
            interface_methods = {}
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                    
                content = Path(file_path).read_text(encoding='utf-8')
                exchange_name = Path(file_path).stem.replace('_ws', '')
                
                # 提取方法签名
                import re
                methods = re.findall(r'async def (\w+)\([^)]*\):', content)
                interface_methods[exchange_name] = set(methods)
            
            # 检查三个交易所接口一致性
            if len(interface_methods) >= 2:
                all_methods = set()
                for methods in interface_methods.values():
                    all_methods.update(methods)
                
                inconsistent_interfaces = 0
                for method in all_methods:
                    exchanges_with_method = [ex for ex, methods in interface_methods.items() if method in methods]
                    if len(exchanges_with_method) != len(interface_methods):
                        inconsistent_interfaces += 1
                        details.append(f"⚠️ 方法 {method} 在交易所间不一致: {exchanges_with_method}")
                
                if inconsistent_interfaces == 0:
                    details.append("✅ 三交易所接口完全一致")
                else:
                    score -= inconsistent_interfaces * 10
            
            # 检查重复代码块
            code_blocks = {}
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                    
                content = Path(file_path).read_text(encoding='utf-8')
                exchange_name = Path(file_path).stem.replace('_ws', '')
                
                # 提取重要代码块的特征
                important_blocks = re.findall(r'(async def \w+.*?(?=async def|\Z))', content, re.DOTALL)
                code_blocks[exchange_name] = important_blocks
            
            # 检查链路兼容性
            chain_compatibility_issues = 0
            for file_path in self.modified_files:
                if not Path(file_path).exists():
                    continue
                content = Path(file_path).read_text(encoding='utf-8')
                
                # 检查可能的链路断裂点
                chain_breaks = [
                    'return None.*# Error',
                    'raise.*# Chain break',
                    'pass.*# Skip chain'
                ]
                
                for break_pattern in chain_breaks:
                    if re.search(break_pattern, content, re.IGNORECASE):
                        chain_compatibility_issues += 1
            
            if chain_compatibility_issues > 0:
                score -= chain_compatibility_issues * 15
                details.append(f"⚠️ 发现 {chain_compatibility_issues} 个潜在链路兼容性问题")
            else:
                details.append("✅ 没有发现链路兼容性问题")
            
            # 检查配置一致性
            config_patterns = [
                'heartbeat_interval',
                'connection_timeout', 
                'rate_limit',
                'batch_size'
            ]
            
            config_values = {}
            for pattern in config_patterns:
                config_values[pattern] = {}
                for file_path in self.modified_files:
                    if not Path(file_path).exists():
                        continue
                    content = Path(file_path).read_text(encoding='utf-8')
                    exchange_name = Path(file_path).stem.replace('_ws', '')
                    
                    # 提取配置值
                    matches = re.findall(f'{pattern}\\s*=\\s*([\\d.]+)', content)
                    if matches:
                        config_values[pattern][exchange_name] = matches[0]
            
            config_inconsistencies = 0
            for config, values in config_values.items():
                if len(set(values.values())) > 1:  # 有不同的值
                    config_inconsistencies += 1
                    details.append(f"⚠️ 配置 {config} 在交易所间不一致: {values}")
            
            if config_inconsistencies == 0 and config_values:
                details.append("✅ 关键配置在交易所间保持一致")
            elif config_inconsistencies > 0:
                score -= config_inconsistencies * 8
            
            passed = score >= 80
            details.append(f"🎯 冗余冲突审查得分: {score}/100 ({'通过' if passed else '不通过'})")
            
        except Exception as e:
            details.append(f"❌ 冗余冲突审查异常: {e}")
            score = 0
            passed = False
        
        return {'score': score, 'details': details, 'passed': passed}

    async def run_comprehensive_audit(self) -> Dict[str, Any]:
        """运行机构级综合审查"""
        logger.info("🚀 开始机构级修复质量综合审查...")
        
        # 执行6项核心审查
        self.audit_results['unified_modules_usage'] = self.audit_1_unified_modules_usage()
        self.audit_results['no_wheel_reinvention'] = self.audit_2_no_wheel_reinvention()
        self.audit_results['no_new_issues'] = self.audit_3_no_new_issues()
        self.audit_results['perfect_fix'] = self.audit_4_perfect_fix()
        self.audit_results['functionality_assured'] = self.audit_5_functionality_assured()
        self.audit_results['no_redundancy_conflicts'] = self.audit_6_no_redundancy_conflicts()
        
        # 计算总分
        total_score = sum(result['score'] for result in self.audit_results.values() if isinstance(result, dict))
        max_score = 600  # 6个审查项 × 100分
        overall_score = int(total_score / max_score * 100)
        
        # 统计通过项目
        passed_audits = sum(1 for result in self.audit_results.values() 
                           if isinstance(result, dict) and result.get('passed', False))
        
        self.audit_results['overall_quality'] = {
            'score': overall_score,
            'passed': overall_score >= 80 and passed_audits >= 5,
            'passed_audits': f"{passed_audits}/6"
        }
        
        # 生成审查报告
        await self._generate_audit_report()
        
        return self.audit_results

    async def _generate_audit_report(self):
        """生成机构级审查报告"""
        logger.info("\n" + "="*100)
        logger.info("🏛️ 机构级修复质量审查报告")
        logger.info("="*100)
        
        # 详细审查结果
        audit_names = [
            "1️⃣ 统一模块使用",
            "2️⃣ 避免重复造轮子", 
            "3️⃣ 新问题引入控制",
            "4️⃣ 完美修复验证",
            "5️⃣ 功能实现确保",
            "6️⃣ 冗余冲突消除"
        ]
        
        audit_keys = [
            'unified_modules_usage',
            'no_wheel_reinvention',
            'no_new_issues', 
            'perfect_fix',
            'functionality_assured',
            'no_redundancy_conflicts'
        ]
        
        for i, (name, key) in enumerate(zip(audit_names, audit_keys)):
            result = self.audit_results[key]
            status = "✅ 通过" if result['passed'] else "❌ 未通过"
            logger.info(f"{name}: {result['score']}/100 {status}")
            
            # 显示关键细节
            for detail in result['details'][:3]:  # 只显示前3个关键细节
                logger.info(f"   {detail}")
        
        # 总体评估
        overall = self.audit_results['overall_quality']
        logger.info(f"\n🎯 总体质量评估: {overall['score']}/100")
        logger.info(f"📊 通过审查项目: {overall['passed_audits']}")
        
        if overall['passed']:
            logger.info("🏆 机构级质量标准: ✅ 通过")
            logger.info("🚀 修复质量达到机构级标准，可以部署到生产环境")
        else:
            logger.info("🚨 机构级质量标准: ❌ 未通过") 
            logger.info("⚠️ 修复需要进一步优化才能达到机构级标准")
        
        logger.info("="*100)

async def main():
    """主函数"""
    auditor = InstitutionalQualityAuditor()
    results = await auditor.run_comprehensive_audit()
    
    # 保存审查结果
    result_file = f"/tmp/institutional_quality_audit_{int(time.time())}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"🎯 审查结果已保存到: {result_file}")
    
    return results['overall_quality']['passed']

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)