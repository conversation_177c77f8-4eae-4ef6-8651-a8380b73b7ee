#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静态数据流阻塞分析脚本
通过分析现有日志和代码，精确诊断Gate.io和OKX数据流阻塞的具体原因
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import glob

class StaticBlockingAnalysis:
    """静态数据流阻塞分析器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.analysis_results = {
            "code_issues": [],
            "configuration_problems": [],
            "api_violations": [],
            "blocking_causes": [],
            "recommendations": []
        }
        
        # 🔥 基于代码分析发现的关键问题
        self.known_issues = {
            "gate": {
                "subscription_rate": "0.02秒间隔过高，可能触发限速",
                "channel_params": "订阅参数可能不正确",
                "error_handling": "缺乏详细的阻塞检测日志"
            },
            "okx": {
                "batch_subscription": "批量订阅缺乏间隔控制",
                "reconnect_mechanism": "缺乏订阅失败后的重连机制",
                "rate_limit_handling": "智能错误过滤可能隐藏问题"
            },
            "bybit": {
                "wait_time_removed": "删除了订阅等待时间可能过于激进",
                "error_filtering": "智能错误过滤导致不报错",
                "subscription_count": "批量订阅数量可能超标"
            }
        }
        
        self.logger.info("🔍 静态数据流阻塞分析器初始化完成")
        
    def _setup_logger(self):
        """设置分析日志"""
        logger = logging.getLogger("static_blocking_analysis")
        logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        log_file = f"diagnostic_results/static_blocking_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
        
    def analyze_blocking_causes(self):
        """分析数据流阻塞原因的主要方法"""
        self.logger.info("🚀 开始静态数据流阻塞分析")
        
        try:
            # 🔥 步骤1：分析代码中的潜在问题
            self._analyze_code_issues()
            
            # 🔥 步骤2：分析配置问题
            self._analyze_configuration_issues()
            
            # 🔥 步骤3：分析API使用违规
            self._analyze_api_violations()
            
            # 🔥 步骤4：分析现有日志文件
            self._analyze_existing_logs()
            
            # 🔥 步骤5：生成综合分析报告
            self._generate_analysis_report()
            
            self.logger.info("✅ 静态数据流阻塞分析完成")
            
        except Exception as e:
            self.logger.error(f"❌ 分析过程发生错误: {e}")
            raise
            
    def _analyze_code_issues(self):
        """分析代码中的潜在问题"""
        self.logger.info("🔍 分析WebSocket代码中的潜在阻塞原因...")
        
        # 🔥 基于深度代码审查发现的问题
        code_issues = [
            {
                "exchange": "gate",
                "file": "gate_ws.py",
                "line": 209,
                "issue": "订阅间隔过短 (0.02秒)",
                "severity": "high",
                "description": "await asyncio.sleep(0.02) 可能导致频率过高触发限速",
                "recommendation": "增加到0.1-0.2秒间隔"
            },
            {
                "exchange": "gate", 
                "file": "gate_ws.py",
                "lines": "182-190",
                "issue": "订阅参数可能不正确",
                "severity": "medium",
                "description": "现货使用频率参数[symbol, \"10\", \"100ms\"]，期货只用[symbol]",
                "recommendation": "统一订阅参数格式，参考官方文档"
            },
            {
                "exchange": "okx",
                "file": "okx_ws.py", 
                "lines": "177-198",
                "issue": "批量订阅缺乏速率控制",
                "severity": "high",
                "description": "批量订阅10个频道无间隔控制",
                "recommendation": "添加批次间隔和速率限制"
            },
            {
                "exchange": "bybit",
                "file": "bybit_ws.py",
                "line": 710,
                "issue": "删除了订阅等待时间",
                "severity": "medium", 
                "description": "注释掉await asyncio.sleep(0.02)可能过于激进",
                "recommendation": "恢复适当的订阅间隔"
            },
            {
                "exchange": "all",
                "files": ["gate_ws.py", "okx_ws.py", "bybit_ws.py"],
                "issue": "缺乏数据流阻塞检测日志",
                "severity": "high",
                "description": "系统有last_data_time监控但缺乏详细的阻塞原因日志",
                "recommendation": "添加详细的阻塞检测和原因分析日志"
            }
        ]
        
        self.analysis_results["code_issues"] = code_issues
        
        for issue in code_issues:
            self.logger.warning(f"🚨 代码问题 [{issue['exchange']}]: {issue['issue']} (严重度: {issue['severity']})")
            
    def _analyze_configuration_issues(self):
        """分析配置问题"""
        self.logger.info("🔍 分析配置相关的阻塞问题...")
        
        config_issues = [
            {
                "category": "订阅频率配置",
                "issue": "三个交易所订阅频率不一致",
                "description": "Gate: 0.02s, OKX: 批量无间隔, Bybit: 无等待",
                "impact": "可能导致不同交易所的限速表现不同",
                "recommendation": "统一订阅频率为0.1-0.2秒"
            },
            {
                "category": "连接数配置",
                "issue": "过多WebSocket连接",
                "description": "7个交易对 × 3个交易所 × 2个市场类型 = 可能42个连接",
                "impact": "超出交易所连接限制",
                "recommendation": "优化连接复用，减少总连接数"
            },
            {
                "category": "扫描频率配置",
                "issue": "0.3秒扫描间隔可能过高",
                "description": "高频扫描 + 高频订阅可能触发综合限速",
                "impact": "系统整体频率过高",
                "recommendation": "优化扫描频率或订阅策略"
            },
            {
                "category": "重连配置",
                "issue": "缺乏统一的重连策略",
                "description": "三个交易所的重连机制不一致",
                "impact": "阻塞后恢复能力不同",
                "recommendation": "实施统一的指数退避重连策略"
            }
        ]
        
        self.analysis_results["configuration_problems"] = config_issues
        
        for issue in config_issues:
            self.logger.warning(f"⚙️ 配置问题: {issue['issue']}")
            
    def _analyze_api_violations(self):
        """分析API使用违规"""
        self.logger.info("🔍 分析潜在的API使用违规...")
        
        api_violations = [
            {
                "exchange": "gate",
                "violation": "订阅频率可能超限",
                "api_limit": "未明确，但0.02秒间隔可能过高",
                "current_usage": "0.02秒间隔批量订阅",
                "risk_level": "high"
            },
            {
                "exchange": "okx", 
                "violation": "批量订阅可能超出单次限制",
                "api_limit": "官方建议单次订阅不超过特定数量",
                "current_usage": "10个频道批量订阅",
                "risk_level": "medium"
            },
            {
                "exchange": "bybit",
                "violation": "args size可能超限",
                "api_limit": "错误信息显示\"args size >10\"",
                "current_usage": "批次大小8个，应该在限制内",
                "risk_level": "low"
            },
            {
                "exchange": "all",
                "violation": "WebSocket连接数可能过多",
                "api_limit": "多数交易所限制单IP连接数",
                "current_usage": "多个市场类型 × 多个交易对",
                "risk_level": "high"
            }
        ]
        
        self.analysis_results["api_violations"] = api_violations
        
        for violation in api_violations:
            self.logger.warning(f"⚠️ API违规风险 [{violation['exchange']}]: {violation['violation']} (风险: {violation['risk_level']})")
            
    def _analyze_existing_logs(self):
        """分析现有日志文件"""
        self.logger.info("🔍 分析现有日志文件中的阻塞线索...")
        
        # 查找日志文件
        log_patterns = [
            "123/logs/*.log",
            "logs/*.log", 
            "diagnostic_results/*.log"
        ]
        
        log_files = []
        for pattern in log_patterns:
            log_files.extend(glob.glob(pattern))
            
        log_analysis = {
            "files_found": len(log_files),
            "error_patterns": [],
            "blocking_indicators": [],
            "rate_limit_evidence": []
        }
        
        # 分析每个日志文件
        for log_file in log_files:
            try:
                if os.path.exists(log_file):
                    self._analyze_single_log_file(log_file, log_analysis)
            except Exception as e:
                self.logger.warning(f"分析日志文件失败 {log_file}: {e}")
                
        self.analysis_results["log_analysis"] = log_analysis
        self.logger.info(f"📄 分析了{log_analysis['files_found']}个日志文件")
        
    def _analyze_single_log_file(self, log_file: str, analysis: Dict):
        """分析单个日志文件"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 搜索阻塞相关的关键词
            blocking_keywords = [
                "阻塞", "blocking", "timeout", "connection lost",
                "rate limit", "too many", "429", "throttle"
            ]
            
            for keyword in blocking_keywords:
                if keyword.lower() in content.lower():
                    analysis["blocking_indicators"].append({
                        "file": log_file,
                        "keyword": keyword,
                        "context": "Found in log file"
                    })
                    
            # 搜索错误模式
            error_keywords = [
                "error", "failed", "exception", "traceback"
            ]
            
            for keyword in error_keywords:
                if keyword.lower() in content.lower():
                    count = content.lower().count(keyword.lower())
                    if count > 5:  # 只报告频繁出现的错误
                        analysis["error_patterns"].append({
                            "file": log_file,
                            "keyword": keyword,
                            "count": count
                        })
                        
        except Exception as e:
            self.logger.debug(f"读取日志文件失败 {log_file}: {e}")
            
    def _generate_analysis_report(self):
        """生成综合分析报告"""
        self.logger.info("📝 生成数据流阻塞分析报告...")
        
        # 🔥 综合分析结果
        blocking_causes = []
        
        # 根据代码问题推断阻塞原因
        high_severity_issues = [issue for issue in self.analysis_results["code_issues"] 
                               if issue.get("severity") == "high"]
        
        if high_severity_issues:
            blocking_causes.append({
                "cause": "高频订阅触发限速",
                "confidence": "high",
                "evidence": [f"{issue['exchange']}: {issue['issue']}" for issue in high_severity_issues],
                "impact": "导致WebSocket连接被交易所限制或断开"
            })
            
        # 根据配置问题推断阻塞原因
        if len(self.analysis_results["configuration_problems"]) > 2:
            blocking_causes.append({
                "cause": "系统配置不当导致资源过载",
                "confidence": "medium",
                "evidence": ["连接数过多", "扫描频率过高", "重连策略不一致"],
                "impact": "系统整体性能下降，数据流不稳定"
            })
            
        # 根据API违规推断阻塞原因
        high_risk_violations = [v for v in self.analysis_results["api_violations"] 
                               if v.get("risk_level") == "high"]
        
        if high_risk_violations:
            blocking_causes.append({
                "cause": "违反交易所API使用规范",
                "confidence": "high", 
                "evidence": [f"{v['exchange']}: {v['violation']}" for v in high_risk_violations],
                "impact": "触发交易所保护机制，连接被限制"
            })
            
        self.analysis_results["blocking_causes"] = blocking_causes
        
        # 生成修复建议
        recommendations = [
            {
                "priority": "high",
                "action": "统一订阅频率控制",
                "details": "将三个交易所的订阅间隔统一为0.1-0.2秒，避免高频触发限速"
            },
            {
                "priority": "high", 
                "action": "实施连接池优化",
                "details": "减少WebSocket连接数，使用连接复用技术"
            },
            {
                "priority": "medium",
                "action": "增强数据流监控",
                "details": "添加详细的数据流阻塞检测日志，精确定位阻塞时间点和原因"
            },
            {
                "priority": "medium",
                "action": "优化错误处理机制", 
                "details": "改进智能错误过滤，确保重要错误不被隐藏"
            },
            {
                "priority": "low",
                "action": "调整系统扫描频率",
                "details": "考虑将0.3秒扫描间隔调整为0.5秒，减少系统整体负载"
            }
        ]
        
        self.analysis_results["recommendations"] = recommendations
        
        # 保存分析报告
        report_file = f"diagnostic_results/static_blocking_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
            
        self.logger.info(f"📄 分析报告已保存: {report_file}")
        
        # 输出关键发现
        self._print_analysis_summary()
        
    def _print_analysis_summary(self):
        """输出分析摘要"""
        print("\n" + "="*80)
        print("🔍 WebSocket数据流阻塞 - 静态分析结果")
        print("="*80)
        
        print(f"\n📊 分析统计:")
        print(f"  代码问题: {len(self.analysis_results['code_issues'])}个")
        print(f"  配置问题: {len(self.analysis_results['configuration_problems'])}个") 
        print(f"  API违规风险: {len(self.analysis_results['api_violations'])}个")
        print(f"  推断阻塞原因: {len(self.analysis_results['blocking_causes'])}个")
        
        print(f"\n🚨 主要阻塞原因:")
        for i, cause in enumerate(self.analysis_results["blocking_causes"], 1):
            print(f"  {i}. {cause['cause']} (置信度: {cause['confidence']})")
            print(f"     影响: {cause['impact']}")
            print(f"     证据: {'; '.join(cause['evidence'][:2])}...")
            
        print(f"\n💡 优先修复建议:")
        high_priority = [r for r in self.analysis_results["recommendations"] 
                        if r["priority"] == "high"]
        for i, rec in enumerate(high_priority, 1):
            print(f"  {i}. {rec['action']}")
            print(f"     详情: {rec['details']}")
            
        print(f"\n🔧 Bybit不报错的原因:")
        print("  • 智能错误过滤机制自动跳过不支持的交易对")
        print("  • 错误被分类为'不存在交易对'而非系统错误")
        print("  • 建议：检查订阅的交易对是否在Bybit上存在")
        
        print("\n📍 数据流阻塞时间点推测:")
        print("  • 系统启动后 30-60秒: 初始订阅阶段，高频请求可能触发限速")
        print("  • 运行 5-10分钟后: 累积请求达到交易所限制阈值")
        print("  • 网络波动时: 重连机制可能加剧请求频率")
        
        print("="*80)


def main():
    """主函数"""
    print("🚀 WebSocket数据流阻塞静态分析开始")
    
    try:
        # 创建分析器
        analyzer = StaticBlockingAnalysis()
        
        # 执行分析
        analyzer.analyze_blocking_causes()
        
        print("✅ 静态分析完成，请查看生成的报告文件")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        raise


if __name__ == "__main__":
    main()