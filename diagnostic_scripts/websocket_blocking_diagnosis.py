#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞诊断脚本
精确诊断Gate.io和OKX数据流阻塞的具体原因和时间点
"""

import asyncio
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "123"))

class DataFlowBlockingDiagnostic:
    """数据流阻塞诊断器"""
    
    def __init__(self, settings=None):
        self.settings = settings
        self.logger = self._setup_logger()
        
        # 🔥 核心监控指标
        self.exchange_status = {
            "gate": {
                "last_data_time": 0,
                "total_messages": 0,
                "blocking_count": 0,
                "blocking_durations": [],
                "subscription_failures": 0,
                "connection_drops": 0,
                "error_patterns": []
            },
            "okx": {
                "last_data_time": 0, 
                "total_messages": 0,
                "blocking_count": 0,
                "blocking_durations": [],
                "subscription_failures": 0,
                "connection_drops": 0,
                "error_patterns": []
            },
            "bybit": {
                "last_data_time": 0,
                "total_messages": 0,
                "blocking_count": 0,
                "blocking_durations": [],
                "subscription_failures": 0,
                "connection_drops": 0,
                "error_patterns": []
            }
        }
        
        # 🔥 阻塞检测配置
        self.blocking_threshold = 30  # 30秒无数据认为阻塞
        self.monitoring_duration = 300  # 监控5分钟
        self.check_interval = 1  # 每秒检查一次
        
        # 🔥 诊断结果
        self.diagnostic_results = {
            "blocking_events": [],
            "rate_limit_violations": [],
            "subscription_issues": [],
            "performance_metrics": {},
            "recommendations": []
        }
        
        self.start_time = time.time()
        self.logger.info("🔍 WebSocket数据流阻塞诊断器初始化完成")
        
    def _setup_logger(self):
        """设置诊断日志"""
        logger = logging.getLogger("websocket_blocking_diagnosis")
        logger.setLevel(logging.DEBUG)
        
        # 创建文件处理器
        log_file = f"diagnostic_results/websocket_blocking_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
        
    async def diagnose_websocket_blocking(self):
        """主诊断方法"""
        self.logger.info("🚀 开始WebSocket数据流阻塞诊断")
        
        try:
            # 🔥 步骤1：初始化WebSocket连接并监控
            await self._initialize_websocket_monitoring()
            
            # 🔥 步骤2：实时监控数据流
            await self._monitor_data_flow()
            
            # 🔥 步骤3：分析阻塞模式
            await self._analyze_blocking_patterns()
            
            # 🔥 步骤4：生成诊断报告
            await self._generate_diagnostic_report()
            
            self.logger.info("✅ WebSocket数据流阻塞诊断完成")
            
        except Exception as e:
            self.logger.error(f"❌ 诊断过程发生错误: {e}")
            raise
            
    async def _initialize_websocket_monitoring(self):
        """初始化WebSocket监控"""
        self.logger.info("🔧 初始化WebSocket监控系统...")
        
        # 🔥 模拟创建WebSocket客户端
        from websocket.gate_ws import GateWebSocketClient
        from websocket.okx_ws import OKXWebSocketClient
        from websocket.bybit_ws import BybitWebSocketClient
        
        # 创建测试用的交易对列表
        test_symbols = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
        
        self.clients = {}
        
        try:
            # Gate.io客户端
            self.clients["gate_spot"] = GateWebSocketClient("spot")
            self.clients["gate_futures"] = GateWebSocketClient("futures")
            
            # OKX客户端
            self.clients["okx_spot"] = OKXWebSocketClient("spot")
            self.clients["okx_futures"] = OKXWebSocketClient("futures")
            
            # Bybit客户端
            self.clients["bybit_spot"] = BybitWebSocketClient("spot")
            self.clients["bybit_futures"] = BybitWebSocketClient("futures")
            
            # 设置交易对
            for client_name, client in self.clients.items():
                if "gate" in client_name:
                    gate_symbols = [s.replace("-", "_") for s in test_symbols]
                    client.set_symbols(gate_symbols)
                else:
                    client.set_symbols(test_symbols)
                    
                # 注册回调函数监控数据流
                client.register_callback("market_data", 
                    lambda data, exchange=client_name.split("_")[0]: self._on_market_data(exchange, data))
                    
            self.logger.info(f"✅ 初始化了{len(self.clients)}个WebSocket客户端")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket客户端初始化失败: {e}")
            
    def _on_market_data(self, exchange: str, data: Dict[str, Any]):
        """数据流回调函数"""
        current_time = time.time()
        
        if exchange in self.exchange_status:
            status = self.exchange_status[exchange]
            
            # 🔥 检测数据流阻塞恢复
            if status["last_data_time"] > 0:
                gap = current_time - status["last_data_time"]
                if gap > self.blocking_threshold:
                    # 记录阻塞事件
                    blocking_event = {
                        "exchange": exchange,
                        "start_time": status["last_data_time"],
                        "end_time": current_time,
                        "duration": gap,
                        "data_type": data.get("type", "unknown")
                    }
                    self.diagnostic_results["blocking_events"].append(blocking_event)
                    status["blocking_count"] += 1
                    status["blocking_durations"].append(gap)
                    
                    self.logger.warning(f"🚨 {exchange}数据流阻塞检测: {gap:.1f}秒")
                    
            # 更新状态
            status["last_data_time"] = current_time  
            status["total_messages"] += 1
            
    async def _monitor_data_flow(self):
        """实时监控数据流"""
        self.logger.info(f"👀 开始监控数据流 - 持续{self.monitoring_duration}秒")
        
        # 🔥 创建监控任务
        monitor_tasks = []
        
        # 启动所有WebSocket客户端
        for client_name, client in self.clients.items():
            task = asyncio.create_task(self._run_client_with_monitoring(client_name, client))
            monitor_tasks.append(task)
            
        # 启动阻塞检测任务
        blocking_detector_task = asyncio.create_task(self._blocking_detector())
        monitor_tasks.append(blocking_detector_task)
        
        # 启动性能监控任务
        performance_monitor_task = asyncio.create_task(self._performance_monitor())
        monitor_tasks.append(performance_monitor_task)
        
        # 等待监控完成或超时
        try:
            await asyncio.wait_for(
                asyncio.gather(*monitor_tasks, return_exceptions=True),
                timeout=self.monitoring_duration
            )
        except asyncio.TimeoutError:
            self.logger.info("⏰ 监控时间到达，停止监控")
            
        # 停止所有客户端
        for client in self.clients.values():
            if hasattr(client, 'running'):
                client.running = False
                
    async def _run_client_with_monitoring(self, client_name: str, client):
        """运行客户端并监控连接状态"""
        try:
            self.logger.info(f"🚀 启动{client_name}客户端监控")
            
            # 记录连接开始时间
            connect_start = time.time()
            
            # 尝试运行客户端
            await client.run()
            
        except Exception as e:
            connect_duration = time.time() - connect_start
            
            # 记录连接错误
            exchange = client_name.split("_")[0]
            if exchange in self.exchange_status:
                self.exchange_status[exchange]["connection_drops"] += 1
                self.exchange_status[exchange]["error_patterns"].append({
                    "error": str(e),
                    "time": time.time(),
                    "connect_duration": connect_duration
                })
                
            self.logger.error(f"❌ {client_name}连接失败: {e}")
            
            # 分析是否为限速问题
            if any(keyword in str(e).lower() for keyword in 
                   ["rate limit", "too many", "429", "throttle", "limit exceeded"]):
                self.diagnostic_results["rate_limit_violations"].append({
                    "exchange": exchange,
                    "client": client_name,
                    "error": str(e),
                    "time": time.time()
                })
                self.logger.warning(f"🚨 检测到{exchange}限速问题")
                
    async def _blocking_detector(self):
        """阻塞检测器"""
        self.logger.info("🔍 启动数据流阻塞检测器")
        
        while True:
            await asyncio.sleep(self.check_interval)
            
            current_time = time.time()
            
            for exchange, status in self.exchange_status.items():
                if status["last_data_time"] > 0:
                    gap = current_time - status["last_data_time"]
                    
                    if gap > self.blocking_threshold:
                        # 🔥 详细记录阻塞原因
                        self.logger.warning(
                            f"⚠️ {exchange}数据流可能阻塞: {gap:.1f}秒无数据, "
                            f"总消息:{status['total_messages']}, "
                            f"阻塞次数:{status['blocking_count']}"
                        )
                        
                        # 检查是否需要记录新的阻塞事件
                        if (not status["blocking_durations"] or 
                            gap - max(status["blocking_durations"], default=0) > 5):
                            
                            # 🔥 尝试获取更多诊断信息
                            await self._diagnose_blocking_cause(exchange, gap)
            
    async def _diagnose_blocking_cause(self, exchange: str, gap: float):
        """诊断阻塞具体原因"""
        self.logger.info(f"🔍 分析{exchange}阻塞原因 - 已阻塞{gap:.1f}秒")
        
        # 🔥 分析可能的原因
        possible_causes = []
        
        status = self.exchange_status[exchange]
        
        # 检查订阅失败
        if status["subscription_failures"] > 0:
            possible_causes.append("订阅失败")
            
        # 检查连接断开
        if status["connection_drops"] > 0:
            possible_causes.append("连接断开")
            
        # 检查错误模式
        if status["error_patterns"]:
            recent_errors = [e for e in status["error_patterns"] 
                           if time.time() - e["time"] < 60]  # 最近1分钟的错误
            if recent_errors:
                possible_causes.append("近期错误频发")
                
        # 检查消息频率
        if status["total_messages"] < 5:  # 消息数太少
            possible_causes.append("消息频率异常低")
            
        # 记录诊断结果
        diagnosis = {
            "exchange": exchange,
            "blocking_duration": gap,
            "possible_causes": possible_causes,
            "time": time.time(),
            "status_snapshot": status.copy()
        }
        
        self.diagnostic_results["blocking_events"].append(diagnosis)
        
        self.logger.warning(f"🔍 {exchange}阻塞分析: {', '.join(possible_causes) if possible_causes else '原因不明'}")
        
    async def _performance_monitor(self):
        """性能监控器"""
        self.logger.info("📊 启动性能监控器")
        
        while True:
            await asyncio.sleep(10)  # 每10秒记录一次性能指标
            
            current_time = time.time()
            elapsed = current_time - self.start_time
            
            # 计算整体性能指标
            total_messages = sum(status["total_messages"] for status in self.exchange_status.values())
            total_blocking = sum(status["blocking_count"] for status in self.exchange_status.values())
            
            self.logger.info(
                f"📊 性能监控 - 运行{elapsed:.0f}秒: "
                f"总消息{total_messages}, 阻塞事件{total_blocking}"
            )
            
    async def _analyze_blocking_patterns(self):
        """分析阻塞模式"""
        self.logger.info("🔍 分析数据流阻塞模式")
        
        # 🔥 分析阻塞频率模式
        blocking_analysis = {}
        
        for exchange, status in self.exchange_status.items():
            if status["blocking_durations"]:
                blocking_analysis[exchange] = {
                    "total_blocks": len(status["blocking_durations"]),
                    "avg_duration": sum(status["blocking_durations"]) / len(status["blocking_durations"]),
                    "max_duration": max(status["blocking_durations"]),
                    "blocking_frequency": len(status["blocking_durations"]) / (time.time() - self.start_time) * 60  # 每分钟
                }
            else:
                blocking_analysis[exchange] = {
                    "total_blocks": 0,
                    "avg_duration": 0,
                    "max_duration": 0,
                    "blocking_frequency": 0
                }
                
        self.diagnostic_results["performance_metrics"]["blocking_analysis"] = blocking_analysis
        
        # 🔥 生成诊断建议
        recommendations = []
        
        for exchange, analysis in blocking_analysis.items():
            if analysis["total_blocks"] > 0:
                if analysis["blocking_frequency"] > 1:  # 每分钟超过1次阻塞
                    recommendations.append(f"{exchange}: 阻塞频率过高，建议检查订阅速率和网络连接")
                    
                if analysis["avg_duration"] > 60:  # 平均阻塞超过1分钟
                    recommendations.append(f"{exchange}: 阻塞持续时间过长，建议优化重连机制")
                    
        self.diagnostic_results["recommendations"] = recommendations
        
    async def _generate_diagnostic_report(self):
        """生成诊断报告"""
        self.logger.info("📝 生成WebSocket阻塞诊断报告")
        
        # 🔥 综合分析结果
        report = {
            "diagnostic_summary": {
                "total_runtime": time.time() - self.start_time,
                "exchanges_monitored": len(self.exchange_status),
                "total_blocking_events": len(self.diagnostic_results["blocking_events"]),
                "rate_limit_violations": len(self.diagnostic_results["rate_limit_violations"]),
                "timestamp": datetime.now().isoformat()
            },
            "exchange_status": self.exchange_status,
            "diagnostic_results": self.diagnostic_results
        }
        
        # 保存报告
        report_file = f"diagnostic_results/websocket_blocking_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        self.logger.info(f"📄 诊断报告已保存: {report_file}")
        
        # 🔥 输出关键发现
        self._print_key_findings(report)
        
    def _print_key_findings(self, report):
        """输出关键发现"""
        print("\n" + "="*60)
        print("🔍 WebSocket数据流阻塞诊断 - 关键发现")
        print("="*60)
        
        summary = report["diagnostic_summary"]
        print(f"📊 监控时长: {summary['total_runtime']:.1f}秒")
        print(f"📊 监控交易所: {summary['exchanges_monitored']}个")
        print(f"🚨 阻塞事件: {summary['total_blocking_events']}次")
        print(f"🚨 限速违规: {summary['rate_limit_violations']}次")
        
        print("\n📈 各交易所状态:")
        for exchange, status in report["exchange_status"].items():
            print(f"  {exchange}: 消息{status['total_messages']}条, 阻塞{status['blocking_count']}次, 连接断开{status['connection_drops']}次")
            
        if report["diagnostic_results"]["recommendations"]:
            print("\n💡 建议:")
            for rec in report["diagnostic_results"]["recommendations"]:
                print(f"  • {rec}")
        else:
            print("\n✅ 未发现明显的阻塞问题")
            
        print("="*60)


async def main():
    """主函数"""
    print("🚀 WebSocket数据流阻塞诊断开始")
    
    try:
        # 创建诊断器
        diagnostic = DataFlowBlockingDiagnostic()
        
        # 执行诊断
        await diagnostic.diagnose_websocket_blocking()
        
        print("✅ 诊断完成，请查看生成的报告文件")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())