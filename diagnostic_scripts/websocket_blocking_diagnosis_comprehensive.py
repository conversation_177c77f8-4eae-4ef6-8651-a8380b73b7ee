#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞问题精确诊断脚本
基于深度代码审查和错误日志分析

🔍 诊断目标：
1. Bybit WebSocket: batch_size变量未定义错误
2. OKX: API限流导致数据阻塞
3. Gate.io: 潜在的订阅频率问题
4. 数据流健康状态检查
"""

import os
import sys
import json
import time
import asyncio
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append('/root/myproject/123/70 gate和okx还是数据阻塞/123')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebSocketBlockingDiagnosis:
    """WebSocket数据流阻塞综合诊断器"""
    
    def __init__(self):
        self.base_path = Path('/root/myproject/123/70 gate和okx还是数据阻塞/123')
        self.diagnosis_results = {
            'timestamp': datetime.now().isoformat(),
            'bybit_issues': [],
            'okx_issues': [],
            'gate_issues': [],
            'data_flow_status': {},
            'critical_errors': [],
            'recommendations': []
        }
    
    def diagnose_bybit_websocket(self):
        """诊断Bybit WebSocket实现问题"""
        logger.info("🔍 诊断Bybit WebSocket实现...")
        
        bybit_ws_path = self.base_path / 'websocket' / 'bybit_ws.py'
        
        if not bybit_ws_path.exists():
            self.diagnosis_results['bybit_issues'].append({
                'type': 'CRITICAL',
                'issue': 'bybit_ws.py文件不存在',
                'location': str(bybit_ws_path)
            })
            return
        
        try:
            with open(bybit_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 🔥 诊断1: batch_size变量未定义错误
            if 'batch_size' in content and 'BATCH_SIZE' in content:
                lines = content.split('\n')
                batch_size_usage = []
                BATCH_SIZE_definition = []
                
                for i, line in enumerate(lines, 1):
                    if 'batch_size' in line and 'BATCH_SIZE' not in line:
                        batch_size_usage.append(f"第{i}行: {line.strip()}")
                    if 'BATCH_SIZE' in line and '=' in line:
                        BATCH_SIZE_definition.append(f"第{i}行: {line.strip()}")
                
                if batch_size_usage and BATCH_SIZE_definition:
                    self.diagnosis_results['bybit_issues'].append({
                        'type': 'CRITICAL',
                        'issue': 'batch_size变量未定义错误',
                        'description': '代码中定义了BATCH_SIZE但使用了batch_size（大小写不一致）',
                        'definition_lines': BATCH_SIZE_definition,
                        'usage_lines': batch_size_usage,
                        'fix': '将batch_size改为BATCH_SIZE'
                    })
            
            # 🔥 诊断2: 订阅频率检查
            if 'await asyncio.sleep(' in content:
                sleep_patterns = []
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if 'await asyncio.sleep(' in line:
                        sleep_patterns.append(f"第{i}行: {line.strip()}")
                
                self.diagnosis_results['bybit_issues'].append({
                    'type': 'INFO',
                    'issue': 'Bybit订阅频率设置',
                    'sleep_patterns': sleep_patterns
                })
            
        except Exception as e:
            self.diagnosis_results['bybit_issues'].append({
                'type': 'ERROR',
                'issue': f'读取bybit_ws.py失败: {e}'
            })
    
    def diagnose_okx_api_limits(self):
        """诊断OKX API限流问题"""
        logger.info("🔍 诊断OKX API限流问题...")
        
        # 检查错误日志中的OKX限流错误
        error_log_path = self.base_path / 'logs' / 'error_20250804.log'
        
        if error_log_path.exists():
            try:
                with open(error_log_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                okx_errors = []
                lines = content.split('\n')
                for line in lines:
                    if 'Too Many Requests' in line and 'OKX' in line:
                        okx_errors.append(line.strip())
                
                if okx_errors:
                    self.diagnosis_results['okx_issues'].append({
                        'type': 'CRITICAL',
                        'issue': 'OKX API限流错误',
                        'error_count': len(okx_errors),
                        'sample_errors': okx_errors[:5],  # 显示前5个错误
                        'description': 'OKX API调用频率过高，触发50011错误码限流'
                    })
                
            except Exception as e:
                self.diagnosis_results['okx_issues'].append({
                    'type': 'ERROR',
                    'issue': f'读取OKX错误日志失败: {e}'
                })
    
    def diagnose_websocket_data_flow(self):
        """诊断WebSocket数据流状态"""
        logger.info("🔍 诊断WebSocket数据流状态...")
        
        # 检查websocket_prices.log的最新时间戳
        prices_log_path = self.base_path / 'logs' / 'websocket_prices.log'
        
        if prices_log_path.exists():
            try:
                # 获取文件最后修改时间
                last_modified = os.path.getmtime(prices_log_path)
                current_time = time.time()
                time_diff = current_time - last_modified
                
                self.diagnosis_results['data_flow_status']['prices_log'] = {
                    'last_modified': datetime.fromtimestamp(last_modified).isoformat(),
                    'seconds_ago': int(time_diff),
                    'status': 'ACTIVE' if time_diff < 60 else 'STALE'
                }
                
                # 读取最后几行检查数据流
                with open(prices_log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        last_lines = lines[-5:]  # 最后5行
                        
                        # 检查三交易所数据是否都存在
                        bybit_count = sum(1 for line in last_lines if 'BYBIT' in line)
                        gate_count = sum(1 for line in last_lines if 'GATE' in line)
                        okx_count = sum(1 for line in last_lines if 'OKX' in line)
                        
                        self.diagnosis_results['data_flow_status']['exchange_data'] = {
                            'bybit_lines': bybit_count,
                            'gate_lines': gate_count,
                            'okx_lines': okx_count,
                            'total_lines': len(last_lines)
                        }
                        
                        # 检查是否有期货数据
                        futures_count = sum(1 for line in last_lines if '期货' in line)
                        spot_count = sum(1 for line in last_lines if '现货' in line)
                        
                        self.diagnosis_results['data_flow_status']['market_data'] = {
                            'futures_lines': futures_count,
                            'spot_lines': spot_count
                        }
                
            except Exception as e:
                self.diagnosis_results['data_flow_status']['error'] = f'读取价格日志失败: {e}'
    
    def diagnose_error_patterns(self):
        """诊断错误模式"""
        logger.info("🔍 分析错误模式...")
        
        error_log_path = self.base_path / 'logs' / 'error_20250804.log'
        
        if error_log_path.exists():
            try:
                with open(error_log_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                
                # 统计各类错误
                error_patterns = {
                    'batch_size_errors': 0,
                    'too_many_requests': 0,
                    'subscription_failures': 0,
                    'bybit_futures_key_zero': 0,
                    'symbol_invalid': 0
                }
                
                for line in lines:
                    if "name 'batch_size' is not defined" in line:
                        error_patterns['batch_size_errors'] += 1
                    elif 'Too Many Requests' in line:
                        error_patterns['too_many_requests'] += 1
                    elif '订阅任务失败' in line or '订阅失败' in line:
                        error_patterns['subscription_failures'] += 1
                    elif 'Bybit期货数据键为0' in line:
                        error_patterns['bybit_futures_key_zero'] += 1
                    elif 'symbol invalid' in line:
                        error_patterns['symbol_invalid'] += 1
                
                self.diagnosis_results['critical_errors'] = error_patterns
                
            except Exception as e:
                self.diagnosis_results['critical_errors'] = {'error': f'读取错误日志失败: {e}'}
    
    def generate_recommendations(self):
        """生成修复建议"""
        logger.info("🔍 生成修复建议...")
        
        recommendations = []
        
        # 基于Bybit问题的建议
        for issue in self.diagnosis_results['bybit_issues']:
            if issue['type'] == 'CRITICAL' and 'batch_size变量未定义' in issue['issue']:
                recommendations.append({
                    'priority': 'HIGH',
                    'title': '修复Bybit WebSocket batch_size变量错误',
                    'description': '将bybit_ws.py中的batch_size改为BATCH_SIZE（统一大小写）',
                    'action': '编辑websocket/bybit_ws.py，修复变量名不一致问题'
                })
        
        # 基于OKX问题的建议
        for issue in self.diagnosis_results['okx_issues']:
            if issue['type'] == 'CRITICAL' and 'API限流' in issue['issue']:
                recommendations.append({
                    'priority': 'HIGH',
                    'title': '优化OKX API调用频率',
                    'description': '降低OKX API调用频率，增加请求间隔',
                    'action': '修改exchanges/okx_exchange.py，增加API调用间隔'
                })
        
        # 基于错误统计的建议
        if self.diagnosis_results['critical_errors'].get('bybit_futures_key_zero', 0) > 0:
            recommendations.append({
                'priority': 'CRITICAL',
                'title': '修复Bybit期货数据键为0问题',
                'description': 'Bybit期货WebSocket订阅失败，导致套利机会检测失败',
                'action': '检查Bybit期货WebSocket订阅逻辑和交易对格式'
            })
        
        self.diagnosis_results['recommendations'] = recommendations
    
    def run_diagnosis(self):
        """运行完整诊断"""
        logger.info("🚀 开始WebSocket数据流阻塞综合诊断...")
        
        self.diagnose_bybit_websocket()
        self.diagnose_okx_api_limits()
        self.diagnose_websocket_data_flow()
        self.diagnose_error_patterns()
        self.generate_recommendations()
        
        # 保存诊断结果
        result_file = f"/tmp/websocket_blocking_diagnosis_{int(time.time())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(self.diagnosis_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 诊断完成，结果保存到: {result_file}")
        
        # 输出摘要
        self.print_summary()
        
        return result_file
    
    def print_summary(self):
        """打印诊断摘要"""
        print("\n" + "="*80)
        print("🔍 WebSocket数据流阻塞诊断摘要")
        print("="*80)
        
        # Bybit问题摘要
        bybit_critical = len([x for x in self.diagnosis_results['bybit_issues'] if x['type'] == 'CRITICAL'])
        print(f"📊 Bybit问题: {len(self.diagnosis_results['bybit_issues'])}个 (关键: {bybit_critical}个)")
        
        # OKX问题摘要
        okx_critical = len([x for x in self.diagnosis_results['okx_issues'] if x['type'] == 'CRITICAL'])
        print(f"📊 OKX问题: {len(self.diagnosis_results['okx_issues'])}个 (关键: {okx_critical}个)")
        
        # 数据流状态
        data_flow = self.diagnosis_results['data_flow_status']
        if 'prices_log' in data_flow:
            status = data_flow['prices_log']['status']
            seconds_ago = data_flow['prices_log']['seconds_ago']
            print(f"📊 数据流状态: {status} (最后更新: {seconds_ago}秒前)")
        
        # 关键错误统计
        critical_errors = self.diagnosis_results['critical_errors']
        if isinstance(critical_errors, dict):
            print(f"📊 关键错误统计:")
            for error_type, count in critical_errors.items():
                if count > 0:
                    print(f"   - {error_type}: {count}次")
        
        # 修复建议
        recommendations = self.diagnosis_results['recommendations']
        high_priority = len([x for x in recommendations if x['priority'] == 'HIGH'])
        critical_priority = len([x for x in recommendations if x['priority'] == 'CRITICAL'])
        print(f"📊 修复建议: {len(recommendations)}个 (关键: {critical_priority}个, 高优先级: {high_priority}个)")
        
        print("="*80)

def main():
    """主函数"""
    diagnosis = WebSocketBlockingDiagnosis()
    result_file = diagnosis.run_diagnosis()
    print(f"\n🎯 详细诊断结果已保存到: {result_file}")
    return result_file

if __name__ == "__main__":
    main()