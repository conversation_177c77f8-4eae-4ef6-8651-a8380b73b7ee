#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据阻塞修复验证脚本

验证已修复的三个关键问题：
1. Bybit WebSocket: batch_size变量未定义错误
2. OKX: API频率限制优化  
3. Gate.io: 心跳间隔规范化
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append('/root/myproject/123/70 gate和okx还是数据阻塞/123')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebSocketFixVerification:
    """WebSocket修复验证器"""
    
    def __init__(self):
        self.base_path = Path('/root/myproject/123/70 gate和okx还是数据阻塞/123')
        self.verification_results = {
            'timestamp': datetime.now().isoformat(),
            'fixes_verified': [],
            'potential_issues': [],
            'overall_status': 'UNKNOWN'
        }
    
    def verify_bybit_batch_size_fix(self):
        """验证Bybit WebSocket batch_size修复"""
        logger.info("🔍 验证Bybit WebSocket batch_size修复...")
        
        bybit_ws_path = self.base_path / 'websocket' / 'bybit_ws.py'
        
        try:
            with open(bybit_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # 检查是否还有batch_size（小写）的错误使用
            batch_size_errors = []
            BATCH_SIZE_correct_usage = []
            
            for i, line in enumerate(lines, 1):
                if 'batch_size' in line and 'BATCH_SIZE' not in line:
                    batch_size_errors.append(f"第{i}行: {line.strip()}")
                elif 'BATCH_SIZE' in line and ('if i +' in line or 'range(' in line):
                    BATCH_SIZE_correct_usage.append(f"第{i}行: {line.strip()}")
            
            if not batch_size_errors and BATCH_SIZE_correct_usage:
                self.verification_results['fixes_verified'].append({
                    'component': 'Bybit WebSocket',
                    'issue': 'batch_size变量未定义错误',
                    'status': 'FIXED',
                    'description': 'batch_size已统一修改为BATCH_SIZE',
                    'correct_usage_count': len(BATCH_SIZE_correct_usage)
                })
            else:
                self.verification_results['potential_issues'].append({
                    'component': 'Bybit WebSocket',
                    'issue': '可能仍存在batch_size错误使用',
                    'remaining_errors': batch_size_errors
                })
            
        except Exception as e:
            self.verification_results['potential_issues'].append({
                'component': 'Bybit WebSocket',
                'issue': f'验证失败: {e}'
            })
    
    def verify_okx_rate_limit_fix(self):
        """验证OKX API频率限制优化"""
        logger.info("🔍 验证OKX API频率限制优化...")
        
        config_path = self.base_path / 'config' / 'unified_api_config.py'
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查OKX rate_limit是否已降低到1
            if 'rate_limit=1' in content and 'okx' in content.lower():
                # 进一步确认是在OKX配置中
                lines = content.split('\n')
                okx_section = False
                rate_limit_found = False
                
                for line in lines:
                    if '"okx"' in line:
                        okx_section = True
                    elif okx_section and 'rate_limit=1' in line:
                        rate_limit_found = True
                        break
                    elif okx_section and '}' in line:
                        break
                
                if rate_limit_found:
                    self.verification_results['fixes_verified'].append({
                        'component': 'OKX API配置',
                        'issue': 'API频率限制过高',
                        'status': 'FIXED',
                        'description': 'OKX rate_limit已优化为1次/秒，减少50011错误',
                        'previous_value': '2次/秒',
                        'current_value': '1次/秒'
                    })
                else:
                    self.verification_results['potential_issues'].append({
                        'component': 'OKX API配置',
                        'issue': '未找到OKX rate_limit=1的配置'
                    })
            else:
                self.verification_results['potential_issues'].append({
                    'component': 'OKX API配置',
                    'issue': '未找到预期的rate_limit=1配置'
                })
            
        except Exception as e:
            self.verification_results['potential_issues'].append({
                'component': 'OKX API配置',
                'issue': f'验证失败: {e}'
            })
    
    def verify_gate_heartbeat_fix(self):
        """验证Gate.io心跳间隔优化"""
        logger.info("🔍 验证Gate.io心跳间隔优化...")
        
        gate_ws_path = self.base_path / 'websocket' / 'gate_ws.py'
        
        try:
            with open(gate_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查heartbeat_interval是否为10
            if 'heartbeat_interval = 10' in content:
                self.verification_results['fixes_verified'].append({
                    'component': 'Gate.io WebSocket',
                    'issue': '心跳间隔配置不规范',
                    'status': 'FIXED',
                    'description': 'heartbeat_interval已优化为10秒，符合官方建议',
                    'previous_value': '5秒',
                    'current_value': '10秒'
                })
            else:
                self.verification_results['potential_issues'].append({
                    'component': 'Gate.io WebSocket',
                    'issue': '未找到heartbeat_interval = 10的配置'
                })
            
        except Exception as e:
            self.verification_results['potential_issues'].append({
                'component': 'Gate.io WebSocket',
                'issue': f'验证失败: {e}'
            })
    
    def check_additional_potential_issues(self):
        """检查其他潜在问题"""
        logger.info("🔍 检查其他潜在问题...")
        
        # 检查错误日志是否还有新的错误
        error_log_path = self.base_path / 'logs' / 'error_20250804.log'
        
        if error_log_path.exists():
            try:
                # 获取文件最后修改时间
                last_modified = os.path.getmtime(error_log_path)
                current_time = time.time()
                time_diff = current_time - last_modified
                
                # 如果错误日志在最近5分钟内有更新，说明可能还有问题
                if time_diff < 300:  # 5分钟
                    self.verification_results['potential_issues'].append({
                        'component': '系统错误日志',
                        'issue': '错误日志在最近5分钟内有更新，可能还有新错误',
                        'last_update': f'{int(time_diff)}秒前'
                    })
                
            except Exception as e:
                self.verification_results['potential_issues'].append({
                    'component': '系统错误日志',
                    'issue': f'检查失败: {e}'
                })
    
    def determine_overall_status(self):
        """确定整体修复状态"""
        fixed_count = len(self.verification_results['fixes_verified'])
        issues_count = len(self.verification_results['potential_issues'])
        
        if fixed_count >= 3 and issues_count == 0:
            self.verification_results['overall_status'] = 'EXCELLENT'
        elif fixed_count >= 2 and issues_count <= 1:
            self.verification_results['overall_status'] = 'GOOD'
        elif fixed_count >= 1:
            self.verification_results['overall_status'] = 'PARTIAL'
        else:
            self.verification_results['overall_status'] = 'FAILED'
    
    def run_verification(self):
        """运行完整验证"""
        logger.info("🚀 开始WebSocket修复验证...")
        
        self.verify_bybit_batch_size_fix()
        self.verify_okx_rate_limit_fix()
        self.verify_gate_heartbeat_fix()
        self.check_additional_potential_issues()
        self.determine_overall_status()
        
        # 保存验证结果
        result_file = f"/tmp/websocket_fix_verification_{int(time.time())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 验证完成，结果保存到: {result_file}")
        
        # 输出摘要
        self.print_summary()
        
        return result_file
    
    def print_summary(self):
        """打印验证摘要"""
        print("\n" + "="*80)
        print("🔍 WebSocket修复验证摘要")
        print("="*80)
        
        # 修复验证结果
        fixed_count = len(self.verification_results['fixes_verified'])
        issues_count = len(self.verification_results['potential_issues'])
        
        print(f"✅ 已验证修复: {fixed_count}个")
        for fix in self.verification_results['fixes_verified']:
            print(f"   - {fix['component']}: {fix['issue']} → {fix['status']}")
        
        print(f"⚠️  潜在问题: {issues_count}个")
        for issue in self.verification_results['potential_issues']:
            print(f"   - {issue['component']}: {issue['issue']}")
        
        # 整体状态
        status = self.verification_results['overall_status']
        status_icons = {
            'EXCELLENT': '🎉',
            'GOOD': '✅',
            'PARTIAL': '⚠️',
            'FAILED': '❌'
        }
        print(f"\n{status_icons.get(status, '❓')} 整体修复状态: {status}")
        
        # 建议
        if status == 'EXCELLENT':
            print("🎯 建议: 所有关键问题已修复，可以重启系统测试效果")
        elif status == 'GOOD':
            print("🎯 建议: 主要问题已修复，建议监控运行状态")
        elif status == 'PARTIAL':
            print("🎯 建议: 部分问题已修复，需要进一步排查剩余问题")
        else:
            print("🎯 建议: 修复验证失败，需要重新检查修复方案")
        
        print("="*80)

def main():
    """主函数"""
    verification = WebSocketFixVerification()
    result_file = verification.run_verification()
    print(f"\n🎯 详细验证结果已保存到: {result_file}")
    return result_file

if __name__ == "__main__":
    main()