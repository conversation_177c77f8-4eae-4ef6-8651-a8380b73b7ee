#!/usr/bin/env python3  
# -*- coding: utf-8 -*-
"""
🔥 复杂系统级联测试：多交易所一致性验证
测试范围：三交易所WebSocket系统联合运行、数据一致性、容错能力
"""

import asyncio
import unittest
import sys
import os
import time
import json
from unittest.mock import Mock, patch, AsyncMock
from decimal import Decimal
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../123'))

class TestSystemIntegration(unittest.TestCase):
    """复杂系统级联测试：多交易所一致性验证"""
    
    def setUp(self):
        """测试初始化"""
        self.test_symbols = ["BTC-USDT", "ETH-USDT"]
        self.exchange_clients = {}
        self.received_data = {"gate": [], "okx": [], "bybit": []}
        self.event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.event_loop)
    
    def tearDown(self):
        """测试清理"""
        self.event_loop.close()
    
    def test_01_multi_exchange_client_creation(self):
        """测试1: 多交易所客户端同时创建"""
        print("\n🧪 测试1: 多交易所客户端同时创建")
        
        try:
            from websocket.gate_ws import GateWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            
            # 同时创建三个交易所的客户端
            self.exchange_clients["gate"] = GateWebSocketClient("spot")
            self.exchange_clients["okx"] = OKXWebSocketClient("spot")
            self.exchange_clients["bybit"] = BybitWebSocketClient("spot")
            
            # 验证所有客户端都成功创建
            for exchange, client in self.exchange_clients.items():
                self.assertIsNotNone(client)
                self.assertEqual(client.market_type, "spot")
                self.assertIsNotNone(client.timestamp_processor)
            
            print("✅ 多交易所客户端同时创建成功")
            
        except Exception as e:
            self.fail(f"❌ 多交易所客户端创建失败: {e}")
    
    def test_02_unified_symbol_setting_consistency(self):
        """测试2: 统一交易对设置一致性"""
        print("\n🧪 测试2: 统一交易对设置一致性")
        
        try:
            # 重用之前创建的客户端或创建新的
            if not self.exchange_clients:
                self.test_01_multi_exchange_client_creation()
            
            # 对所有交易所设置相同的交易对
            for exchange, client in self.exchange_clients.items():
                client.set_symbols(self.test_symbols)
                
                # 验证设置成功
                self.assertTrue(len(client.symbols) > 0)
                
                # 验证格式转换正确性
                if exchange == "gate":
                    # Gate.io应该转换为下划线格式
                    self.assertTrue(all("_" in symbol for symbol in client.symbols))
                elif exchange == "okx":
                    # OKX应该保持连字符格式
                    self.assertTrue(all("-" in symbol for symbol in client.symbols))
                elif exchange == "bybit":
                    # Bybit应该转换为无分隔符格式
                    self.assertTrue(all(symbol.isalnum() for symbol in client.symbols))
            
            print("✅ 统一交易对设置一致性验证通过")
            
        except Exception as e:
            self.fail(f"❌ 统一交易对设置一致性验证失败: {e}")
    
    def test_03_timestamp_processor_consistency(self):
        """测试3: 时间戳处理器一致性"""
        print("\n🧪 测试3: 时间戳处理器一致性")
        
        try:
            if not self.exchange_clients:
                self.test_01_multi_exchange_client_creation()
            
            # 测试不同交易所的时间戳处理
            current_time = int(time.time() * 1000)
            
            test_data = {
                "gate": {"t": current_time},
                "okx": {"ts": str(current_time)},
                "bybit": {"T": current_time}
            }
            
            processed_timestamps = {}
            
            for exchange, client in self.exchange_clients.items():
                timestamp = client.timestamp_processor.get_synced_timestamp(test_data[exchange])
                processed_timestamps[exchange] = timestamp
                
                # 验证时间戳是整数类型
                self.assertIsInstance(timestamp, int)
                
                # 验证时间戳在合理范围内（不超过当前时间前后10秒）
                time_diff = abs(timestamp - current_time)
                self.assertLess(time_diff, 10000)  # 10秒的毫秒数
            
            # 验证所有处理器产生的时间戳相近（考虑同步偏移）
            timestamps = list(processed_timestamps.values())
            max_diff = max(timestamps) - min(timestamps)
            self.assertLess(max_diff, 5000)  # 最大差异不超过5秒
            
            print("✅ 时间戳处理器一致性验证通过")
            
        except Exception as e:
            self.fail(f"❌ 时间戳处理器一致性验证失败: {e}")
    
    def test_04_unified_data_formatter_consistency(self):
        """测试4: 统一数据格式化器一致性"""
        print("\n🧪 测试4: 统一数据格式化器一致性")
        
        try:
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            formatter = get_orderbook_formatter()
            
            # 测试数据
            asks = [["50000.0", "1.0"], ["50001.0", "2.0"]]
            bids = [["49999.0", "1.5"], ["49998.0", "2.5"]]
            timestamp = int(time.time() * 1000)
            
            # 测试三个交易所的数据格式化
            formatted_data = {}
            
            for exchange in ["gate", "okx", "bybit"]:
                data = formatter.format_orderbook_data(
                    asks=asks,
                    bids=bids,
                    symbol="BTC-USDT",
                    exchange=exchange,
                    market_type="spot",
                    timestamp=timestamp
                )
                formatted_data[exchange] = data
                
                # 验证基本字段存在
                required_fields = ["symbol", "exchange", "asks", "bids", "timestamp", "market_type"]
                for field in required_fields:
                    self.assertIn(field, data)
                
                # 验证数据类型
                self.assertEqual(data["symbol"], "BTC-USDT")
                self.assertEqual(data["exchange"], exchange)
                self.assertEqual(data["market_type"], "spot")
                self.assertIsInstance(data["asks"], list)
                self.assertIsInstance(data["bids"], list)
                self.assertIsInstance(data["timestamp"], int)
            
            # 验证所有交易所的格式化结果结构一致
            keys_sets = [set(data.keys()) for data in formatted_data.values()]
            self.assertTrue(all(keys == keys_sets[0] for keys in keys_sets))
            
            print("✅ 统一数据格式化器一致性验证通过")
            
        except Exception as e:
            self.fail(f"❌ 统一数据格式化器一致性验证失败: {e}")
    
    def test_05_orderbook_validator_cross_exchange(self):
        """测试5: 订单簿验证器跨交易所一致性"""
        print("\n🧪 测试5: 订单簿验证器跨交易所一致性")
        
        try:
            from websocket.orderbook_validator import get_orderbook_validator
            
            validator = get_orderbook_validator()
            
            # 测试数据 - 有效订单簿
            valid_data = {
                "asks": [["50000.0", "1.0"], ["50001.0", "2.0"]],
                "bids": [["49999.0", "1.5"], ["49998.0", "2.5"]]
            }
            
            # 测试数据 - 无效订单簿 
            invalid_data = {
                "asks": [],
                "bids": []
            }
            
            # 对所有交易所进行验证
            for exchange in ["gate", "okx", "bybit"]:
                # 验证有效数据
                valid_result = validator.validate_orderbook_data(
                    valid_data, exchange=exchange, symbol="BTC-USDT", market_type="spot"
                )
                self.assertTrue(valid_result.is_valid)
                # 修复：空字符串也视为无错误
                self.assertTrue(valid_result.error_message is None or valid_result.error_message == "")
                
                # 验证无效数据
                invalid_result = validator.validate_orderbook_data(
                    invalid_data, exchange=exchange, symbol="BTC-USDT", market_type="spot"
                )
                self.assertFalse(invalid_result.is_valid)
                self.assertIsNotNone(invalid_result.error_message)
            
            print("✅ 订单簿验证器跨交易所一致性验证通过")
            
        except Exception as e:
            self.fail(f"❌ 订单簿验证器跨交易所一致性验证失败: {e}")
    
    def test_06_websocket_url_configuration_consistency(self):
        """测试6: WebSocket URL配置一致性"""
        print("\n🧪 测试6: WebSocket URL配置一致性")
        
        try:
            if not self.exchange_clients:
                self.test_01_multi_exchange_client_creation()
            
            # 验证每个交易所的URL格式正确性
            expected_urls = {
                "gate": "wss://api.gateio.ws/ws/v4/",
                "okx": "wss://ws.okx.com:8443/ws/v5/public",
                "bybit": "wss://stream.bybit.com/v5/public/spot"
            }
            
            for exchange, client in self.exchange_clients.items():
                url = client.get_ws_url()
                expected_url = expected_urls[exchange]
                
                self.assertEqual(url, expected_url)
                self.assertTrue(url.startswith("wss://"))
                
                # 验证域名正确
                if exchange == "gate":
                    self.assertIn("gateio.ws", url)
                elif exchange == "okx":
                    self.assertIn("okx.com", url)
                elif exchange == "bybit":
                    self.assertIn("bybit.com", url)
            
            print("✅ WebSocket URL配置一致性验证通过")
            
        except Exception as e:
            self.fail(f"❌ WebSocket URL配置一致性验证失败: {e}")
    
    def test_07_error_handling_intelligence_cross_exchange(self):
        """测试7: 跨交易所智能错误处理"""
        print("\n🧪 测试7: 跨交易所智能错误处理")
        
        try:
            # 定义各交易所的典型错误消息
            error_scenarios = {
                "gate": {
                    "unsupported_pair": {"error": {"code": 2, "message": "unknown currency pair"}},
                    "rate_limit": {"error": {"code": 429, "message": "too many requests"}}
                },
                "okx": {
                    "unsupported_pair": {"event": "error", "code": "60018", "msg": "instrument doesn't exist"},
                    "rate_limit": {"event": "error", "code": "30001", "msg": "too many requests"}
                },
                "bybit": {
                    "unsupported_pair": {"op": "subscribe", "success": False, "ret_msg": "symbol not found"},
                    "rate_limit": {"op": "subscribe", "success": False, "ret_msg": "rate limit exceeded"}
                }
            }
            
            # 验证每个交易所都能正确识别和处理不同类型的错误
            for exchange, scenarios in error_scenarios.items():
                for error_type, error_msg in scenarios.items():
                    # 模拟错误处理逻辑
                    if error_type == "unsupported_pair":
                        # 这类错误应该被智能过滤，不影响系统运行
                        should_filter = True
                        
                        # 验证错误消息包含关键词
                        error_text = str(error_msg).lower()
                        contains_filter_keywords = any(
                            keyword in error_text 
                            for keyword in ["unknown", "not found", "doesn't exist", "symbol not found"]
                        )
                        self.assertTrue(contains_filter_keywords)
                        
                    elif error_type == "rate_limit":
                        # 这类错误应该被记录并可能触发重试
                        should_log = True
                        
                        error_text = str(error_msg).lower()
                        contains_rate_limit_keywords = any(
                            keyword in error_text
                            for keyword in ["too many", "rate limit", "429"]
                        )
                        self.assertTrue(contains_rate_limit_keywords)
            
            print("✅ 跨交易所智能错误处理验证通过")
            
        except Exception as e:
            self.fail(f"❌ 跨交易所智能错误处理验证失败: {e}")
    
    def test_08_unified_configuration_cross_validation(self):
        """测试8: 统一配置跨验证"""
        print("\n🧪 测试8: 统一配置跨验证")
        
        try:
            from websocket.unified_websocket_fix import UnifiedWebSocketFixStrategy
            
            config = UnifiedWebSocketFixStrategy.get_optimized_subscription_config()
            
            # 验证配置包含所有必要的交易所特定参数
            required_params = [
                "gate_subscription_interval",
                "okx_batch_size", 
                "okx_batch_interval",
                "bybit_subscription_interval",
                "reconnect_delay",
                "heartbeat_interval",
                "data_timeout"
            ]
            
            for param in required_params:
                self.assertIn(param, config)
                self.assertIsInstance(config[param], (int, float))
                self.assertGreater(config[param], 0)
            
            # 验证参数值在合理范围内
            self.assertLessEqual(config["gate_subscription_interval"], 1.0)
            self.assertLessEqual(config["okx_batch_size"], 20) 
            self.assertLessEqual(config["okx_batch_interval"], 1.0)
            self.assertLessEqual(config["bybit_subscription_interval"], 1.0)
            
            # 验证类常量与配置一致性
            self.assertEqual(config["gate_subscription_interval"], 
                           UnifiedWebSocketFixStrategy.GATE_SUBSCRIPTION_INTERVAL)
            self.assertEqual(config["okx_batch_size"], 
                           UnifiedWebSocketFixStrategy.OKX_BATCH_SIZE)
            self.assertEqual(config["bybit_subscription_interval"], 
                           UnifiedWebSocketFixStrategy.BYBIT_SUBSCRIPTION_INTERVAL)
            
            print("✅ 统一配置跨验证通过")
            
        except Exception as e:
            self.fail(f"❌ 统一配置跨验证失败: {e}")
    
    def test_09_currency_adapter_cross_exchange_normalization(self):
        """测试9: 货币适配器跨交易所标准化"""
        print("\n🧪 测试9: 货币适配器跨交易所标准化")
        
        try:
            from exchanges.currency_adapter import normalize_symbol
            
            # 测试不同交易所格式的标准化
            test_cases = [
                # (输入格式, 预期输出)
                ("BTC_USDT", "BTC-USDT"),    # Gate.io格式
                ("BTC-USDT", "BTC-USDT"),    # OKX格式  
                ("BTCUSDT", "BTC-USDT"),     # Bybit格式
                ("ETH_USDT", "ETH-USDT"),    # 更多测试
                ("ETH-USDT", "ETH-USDT"),
                ("ETHUSDT", "ETH-USDT"),
                ("ADA_USDT", "ADA-USDT"),
                ("ADA-USDT", "ADA-USDT"),
                ("ADAUSDT", "ADA-USDT"),
            ]
            
            for input_symbol, expected_output in test_cases:
                result = normalize_symbol(input_symbol)
                self.assertEqual(result, expected_output, 
                               f"标准化失败: {input_symbol} -> {result} (预期 {expected_output})")
            
            # 验证标准化结果在所有交易所都适用
            standard_symbols = [normalize_symbol(case[0]) for case in test_cases]
            unique_standards = list(set(standard_symbols))
            
            # 所有标准化结果都应该使用连字符格式
            for symbol in unique_standards:
                self.assertIn("-", symbol)
                self.assertNotIn("_", symbol)
                parts = symbol.split("-")
                self.assertEqual(len(parts), 2)  # 应该正好分成两部分
                self.assertTrue(all(part.isalpha() for part in parts))  # 都应该是字母
            
            print("✅ 货币适配器跨交易所标准化验证通过")
            
        except Exception as e:
            self.fail(f"❌ 货币适配器跨交易所标准化验证失败: {e}")
    
    def test_10_system_integration_readiness(self):
        """测试10: 系统集成就绪性"""
        print("\n🧪 测试10: 系统集成就绪性")
        
        try:
            # 综合验证系统各组件的集成就绪性
            
            # 1. 验证统一模块都可以正常导入
            required_modules = [
                "websocket.unified_timestamp_processor",
                "websocket.unified_data_formatter", 
                "websocket.orderbook_validator",
                "websocket.unified_websocket_fix",
                "exchanges.currency_adapter",
                "websocket.gate_ws",
                "websocket.okx_ws", 
                "websocket.bybit_ws"
            ]
            
            imported_modules = {}
            for module_name in required_modules:
                try:
                    module = __import__(module_name, fromlist=[''])
                    imported_modules[module_name] = module
                    self.assertIsNotNone(module)
                except ImportError as e:
                    self.fail(f"关键模块导入失败: {module_name} - {e}")
            
            # 2. 验证所有WebSocket客户端都有必要的方法
            client_classes = [
                imported_modules["websocket.gate_ws"].GateWebSocketClient,
                imported_modules["websocket.okx_ws"].OKXWebSocketClient,
                imported_modules["websocket.bybit_ws"].BybitWebSocketClient
            ]
            
            required_methods = [
                "set_symbols", "get_ws_url", "subscribe_channels", 
                "handle_message", "send_heartbeat"
            ]
            
            for client_class in client_classes:
                for method_name in required_methods:
                    self.assertTrue(hasattr(client_class, method_name))
            
            # 3. 验证统一管理器可以正常工作
            recovery_manager = imported_modules["websocket.unified_websocket_fix"].get_unified_recovery_manager()
            self.assertIsNotNone(recovery_manager)
            self.assertIn("gate", recovery_manager.fixers)
            self.assertIn("okx", recovery_manager.fixers)
            self.assertIn("bybit", recovery_manager.fixers)
            
            # 4. 验证配置系统完整性
            config = imported_modules["websocket.unified_websocket_fix"].UnifiedWebSocketFixStrategy.get_optimized_subscription_config()
            self.assertIsInstance(config, dict)
            self.assertGreater(len(config), 5)  # 应该有多个配置项
            
            print("✅ 系统集成就绪性验证通过")
            
        except Exception as e:
            self.fail(f"❌ 系统集成就绪性验证失败: {e}")

def run_system_integration_tests():
    """运行系统集成测试"""
    print("🚀 开始复杂系统级联测试：多交易所一致性验证")
    print("=" * 70)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestSystemIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 统计结果
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors) 
    success_rate = ((total_tests - failures - errors) / total_tests * 100) if total_tests > 0 else 0
    
    print("\n" + "=" * 70)
    print(f"📊 复杂系统级联测试结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功: {total_tests - failures - errors}")
    print(f"   失败: {failures}")
    print(f"   错误: {errors}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print("✅ 复杂系统级联测试全部通过 - 多交易所一致性达标")
        return True
    else:
        print("❌ 复杂系统级联测试存在问题 - 需要修复")
        
        # 显示失败详情
        if result.failures:
            print("\n❌ 失败详情:")
            for test, error in result.failures:
                print(f"   {test}: {error}")
        
        if result.errors:
            print("\n❌ 错误详情:")
            for test, error in result.errors:
                print(f"   {test}: {error}")
        
        return False

if __name__ == "__main__":
    success = run_system_integration_tests()
    sys.exit(0 if success else 1)